#!/bin/bash

# Gemini 2.5 Flash 聊天测试脚本 (OpenAI 兼容格式)
# 使用提供的令牌测试端口3000的聊天服务

API_TOKEN="sk-aOxnzCfQjl69VGHV18578aFf7fC74cEcB896EcE8Ed8553E2"
BASE_URL="http://localhost:3000/v1"

echo "=== Gemini 2.5 Flash 聊天测试 (OpenAI 兼容接口) ==="
echo "API Token: ${API_TOKEN}"
echo "Base URL: ${BASE_URL}"
echo ""

# 测试简单聊天 (非流式)
echo "1. 测试简单聊天 (非流式)..."
curl -X POST "${BASE_URL}/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {
        "role": "user",
        "content": "你好，请简单介绍一下你自己。"
      }
    ],
    "stream": false,
    "temperature": 0.9,
    "max_tokens": 1024
  }' \
  --no-buffer \
  -v

echo -e "\n\n=== 分隔线 ===\n"

# 测试流式聊天
echo "2. 测试流式聊天..."
curl -X POST "${BASE_URL}/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Accept: text/event-stream" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {
        "role": "user",
        "content": "请写一首关于人工智能的短诗，要求有韵律感。"
      }
    ],
    "stream": true,
    "temperature": 0.9,
    "max_tokens": 1024
  }' \
  --no-buffer \
  -v

echo -e "\n\n=== 分隔线 ===\n"

# 测试多轮对话 (流式)
echo "3. 测试多轮对话 (流式)..."
curl -X POST "${BASE_URL}/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {
        "role": "user",
        "content": "什么是机器学习？"
      },
      {
        "role": "assistant",
        "content": "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。"
      },
      {
        "role": "user",
        "content": "能举个具体的例子吗？"
      }
    ],
    "stream": true,
    "temperature": 0.9,
    "max_tokens": 1024
  }' \
  --no-buffer \
  -v

echo -e "\n\n=== 测试完成 ==="
