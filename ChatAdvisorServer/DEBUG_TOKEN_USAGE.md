# 调试Token使用指南

## 概述

调试Token是为ChatAdvisorServer后端API系统设计的开发工具，允许在开发和测试环境中绕过正常的身份验证流程，便于API调试和测试。

## 安全特性

- **环境隔离**：仅在开发(`development`)和测试(`test`)环境中生效
- **生产环境保护**：在生产环境中自动禁用，即使配置了调试Token也不会生效
- **双重安全检查**：通过`NODE_ENV`和`env.isProduction`进行双重环境验证
- **安全Token**：使用64位十六进制随机字符串，具有足够的复杂度

## 配置

### 环境变量配置

调试Token在`.env.development`文件中配置：

```bash
# 调试Token配置 - 仅在开发/测试环境使用
# 警告：此Token可绕过正常身份验证，绝不能在生产环境使用
DEBUG_TOKEN=DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1
```

### 生产环境配置

在`.env.production`文件中**不要**添加`DEBUG_TOKEN`配置，或者设置为空：

```bash
# 生产环境不应包含调试Token配置
# DEBUG_TOKEN=  # 留空或不配置
```

## 使用方法

### HTTP请求示例

使用调试Token时，在HTTP请求头中添加Authorization字段：

```bash
# 使用curl测试
curl -H "Authorization: Bearer DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1" \
     http://localhost:33001/api/chat

# 使用Postman或其他API测试工具
Authorization: Bearer DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1
```

### JavaScript/前端示例

```javascript
// 在开发环境中使用调试Token
const debugToken = 'DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1';

fetch('/api/chat', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${debugToken}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        message: 'Hello, this is a test message'
    })
});
```

## 工作原理

1. **Token格式检查**：系统检查Token是否以`DEBUG_`开头
2. **环境验证**：确认当前环境为开发或测试环境
3. **Token匹配**：验证Token是否与配置的调试Token完全匹配
4. **请求放行**：如果验证通过，设置特殊的请求上下文并放行请求

## 调试用户信息

系统会自动创建一个专用的调试用户，具有以下信息：
- **用户ID**: `507f1f77bcf86cd799439011`
- **邮箱**: `<EMAIL>`
- **用户名**: `debug_user`
- **余额**: `999999` (大量余额用于测试)
- **角色**: `user`
- **状态**: `active`

## 请求上下文

当使用调试Token时，系统会在请求对象中设置特殊的token信息：

```javascript
req.token = {
    isDebugToken: true,
    debugMode: true,
    userId: '507f1f77bcf86cd799439011', // 真实的调试用户ID
    email: '<EMAIL>',
    type: 'debug'
};
```

## 日志记录

使用调试Token时，系统会记录警告日志，包含以下信息：
- 请求路径
- 客户端IP地址
- User-Agent信息

示例日志：
```
[WARN] 调试Token被使用 - 路径: /api/chat, IP: 127.0.0.1, User-Agent: curl/7.68.0
```

## 注意事项

### ⚠️ 重要警告

1. **绝不在生产环境使用**：调试Token会绕过所有身份验证，在生产环境使用会造成严重安全风险
2. **保护Token安全**：不要将调试Token提交到公共代码仓库或分享给未授权人员
3. **定期更换**：建议定期更换调试Token，特别是在团队成员变动时

### 最佳实践

1. **环境隔离**：确保开发、测试、生产环境的配置文件完全分离
2. **访问控制**：限制对`.env.development`文件的访问权限
3. **监控使用**：定期检查日志，确保调试Token只在预期的开发活动中使用
4. **文档更新**：团队成员变动时及时更新相关文档和配置

## 故障排除

### 调试Token不工作

1. **检查环境**：确认当前运行环境为`development`或`test`
2. **检查配置**：验证`.env.development`中的`DEBUG_TOKEN`配置正确
3. **检查格式**：确认Token以`DEBUG_`开头且完全匹配配置值
4. **检查日志**：查看服务器日志是否有相关错误信息

### 生产环境安全检查

如果在生产环境中意外检测到调试Token使用，系统会：
1. 记录错误日志
2. 拒绝请求
3. 返回未授权错误

## 技术实现

调试Token功能在以下文件中实现：
- `src/config/env.ts`：环境配置导出
- `src/middlewares/verifyTokenHandler.ts`：Token验证逻辑
- `.env.development`：开发环境配置
- `scripts/create-debug-user.js`：调试用户创建脚本

## 调试用户管理

如需重新创建或更新调试用户，可以运行：

```bash
cd ChatAdvisorServer
node scripts/create-debug-user.js
```

该脚本会：
1. 检查调试用户是否已存在
2. 创建或更新调试用户信息
3. 设置大量余额用于测试
4. 验证创建结果

## 更新历史

- **v1.0**：初始实现，支持基本的调试Token功能
- **v1.1**：添加真实调试用户支持，解决数据库查询问题
- 添加环境隔离和安全检查
- 添加详细的日志记录和使用文档
- 添加调试用户自动创建脚本
