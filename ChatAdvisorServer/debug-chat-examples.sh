#!/bin/bash

# ChatAdvisorServer 调试Token聊天测试脚本
# 使用调试Token模拟各种聊天场景

# 配置变量
DEBUG_TOKEN="DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1"
BASE_URL="http://localhost:33001"

echo "🤖 ChatAdvisorServer 调试Token聊天测试"
echo "========================================"
echo "🔑 调试Token: ${DEBUG_TOKEN:0:20}..."
echo "🌐 服务器地址: $BASE_URL"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 1. 基础聊天请求
echo -e "${BLUE}📝 测试1: 基础聊天请求${NC}"
echo "发送简单的问候消息..."
curl -X POST "$BASE_URL/api/chat" \
  -H "Authorization: Bearer $DEBUG_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "你好，请介绍一下你自己"
      }
    ],
    "model": "gpt-3.5-turbo",
    "stream": false
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n\n"

echo -e "${YELLOW}按回车键继续下一个测试...${NC}"
read

# 2. 多轮对话测试
echo -e "${BLUE}📝 测试2: 多轮对话测试${NC}"
echo "模拟多轮对话场景..."
curl -X POST "$BASE_URL/api/chat" \
  -H "Authorization: Bearer $DEBUG_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "我想学习编程，你有什么建议吗？"
      },
      {
        "role": "assistant",
        "content": "学习编程是一个很好的选择！我建议从Python开始，因为它语法简单，适合初学者。你可以先学习基础语法，然后做一些小项目来练习。"
      },
      {
        "role": "user",
        "content": "那我应该从哪些Python基础开始学习呢？"
      }
    ],
    "model": "gpt-3.5-turbo",
    "stream": false
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n\n"

echo -e "${YELLOW}按回车键继续下一个测试...${NC}"
read

# 3. 流式响应测试
echo -e "${BLUE}📝 测试3: 流式响应测试${NC}"
echo "测试流式响应功能..."
curl -X POST "$BASE_URL/api/chat" \
  -H "Authorization: Bearer $DEBUG_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "请写一首关于春天的短诗"
      }
    ],
    "model": "gpt-3.5-turbo",
    "stream": true
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n\n"

echo -e "${YELLOW}按回车键继续下一个测试...${NC}"
read

# 4. 技术问题咨询
echo -e "${BLUE}📝 测试4: 技术问题咨询${NC}"
echo "测试技术相关问题..."
curl -X POST "$BASE_URL/api/chat" \
  -H "Authorization: Bearer $DEBUG_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "如何在JavaScript中实现防抖函数？请提供代码示例。"
      }
    ],
    "model": "gpt-4",
    "stream": false
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n\n"

echo -e "${YELLOW}按回车键继续下一个测试...${NC}"
read

# 5. 长文本处理测试
echo -e "${BLUE}📝 测试5: 长文本处理测试${NC}"
echo "测试长文本输入处理..."
curl -X POST "$BASE_URL/api/chat" \
  -H "Authorization: Bearer $DEBUG_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "请帮我总结以下文本的主要内容：人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的容器。"
      }
    ],
    "model": "gpt-3.5-turbo",
    "stream": false
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n\n"

echo -e "${YELLOW}按回车键继续下一个测试...${NC}"
read

# 6. 错误处理测试 - 无效参数
echo -e "${BLUE}📝 测试6: 错误处理测试 - 无效参数${NC}"
echo "测试无效参数的错误处理..."
curl -X POST "$BASE_URL/api/chat" \
  -H "Authorization: Bearer $DEBUG_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [],
    "model": "invalid-model",
    "stream": "not-boolean"
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n\n"

echo -e "${YELLOW}按回车键继续下一个测试...${NC}"
read

# 7. 兼容性路由测试
echo -e "${BLUE}📝 测试7: 兼容性路由测试${NC}"
echo "测试旧版本API兼容性..."
curl -X POST "$BASE_URL/chat" \
  -H "Authorization: Bearer $DEBUG_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "这是通过兼容性路由发送的消息"
      }
    ]
  }' \
  -w "\n状态码: %{http_code}\n响应时间: %{time_total}s\n\n"

echo -e "${GREEN}✅ 所有测试完成！${NC}"
echo ""
echo "💡 使用提示："
echo "1. 确保服务器在开发环境中运行"
echo "2. 检查服务器日志以查看调试Token使用记录"
echo "3. 如果遇到错误，请检查服务器状态和配置"
echo ""
echo "🔧 单独运行测试："
echo "curl -X POST $BASE_URL/api/chat \\"
echo "  -H \"Authorization: Bearer $DEBUG_TOKEN\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"messages\":[{\"role\":\"user\",\"content\":\"你好\"}]}'"
