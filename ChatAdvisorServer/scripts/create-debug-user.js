#!/usr/bin/env node

/**
 * 创建调试用户脚本
 * 为调试Token功能创建一个专用的调试用户
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境配置
dotenv.config({ path: path.resolve(__dirname, '../.env.development') });
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// 固定的调试用户ID - 使用固定的ObjectId便于调试
const DEBUG_USER_ID = new mongoose.Types.ObjectId('507f1f77bcf86cd799439011');
const DEBUG_USER_EMAIL = '<EMAIL>';
const DEBUG_USER_BALANCE = 999999; // 大量余额用于测试

console.log('🔧 创建调试用户脚本');
console.log('='.repeat(50));
console.log(`📍 调试用户ID: ${DEBUG_USER_ID}`);
console.log(`📧 调试用户邮箱: ${DEBUG_USER_EMAIL}`);
console.log(`💰 调试用户余额: ${DEBUG_USER_BALANCE}`);
console.log('');

// 用户模型定义
const userSchema = new mongoose.Schema({
    username: { type: String },
    password: { type: String },
    email: { type: String, required: true, unique: true },
    fullName: { type: String },
    birthDate: { type: Date },
    gender: { type: String, enum: ['Male', 'Female', 'Other'] },
    phone: { type: String },
    address: {
        street: { type: String },
        city: { type: String },
        state: { type: String },
        country: { type: String },
        postalCode: { type: String }
    },
    language: { type: String, default: 'en' },
    timeZone: { type: String },
    occupation: { type: String },
    company: { type: String },
    allergies: [String],
    medicalConditions: [String],
    balance: { type: Number, default: 50 },
    role: { type: String, enum: ['user', 'admin', 'super_admin'], default: 'user' },
    status: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
    lastLoginAt: { type: Date },
    lastLoginIP: { type: String },
    avatar: { type: String },
    isDelete: { type: Boolean, default: false },
    externalAccounts: {
        weChatId: { type: String, unique: true, sparse: true },
        qqId: { type: String, unique: true, sparse: true }
    },
    hasPurchase: { type: Boolean, default: false},
    twoFactorSecret: { type: String, select: false },
    twoFactorEnabled: { type: Boolean, default: false },
    twoFactorBackupCodes: { type: [String], select: false }
}, {
    versionKey: false,
    timestamps: true
});

// 用户余额模型定义
const userBalanceSchema = new mongoose.Schema({
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    balance: { type: Number, default: 50 }
}, {
    versionKey: false
});

const User = mongoose.model('User', userSchema);
const UserBalance = mongoose.model('UserBalance', userBalanceSchema);

/**
 * 创建或更新调试用户
 */
async function createDebugUser() {
    try {
        console.log('🔍 检查调试用户是否已存在...');
        
        // 检查用户是否已存在
        let existingUser = await User.findById(DEBUG_USER_ID);
        
        if (existingUser) {
            console.log('✅ 调试用户已存在，更新用户信息...');
            
            // 更新用户信息
            await User.findByIdAndUpdate(DEBUG_USER_ID, {
                email: DEBUG_USER_EMAIL,
                username: 'debug_user',
                fullName: 'Debug User',
                language: 'en',
                role: 'user',
                status: 'active',
                isDelete: false,
                balance: DEBUG_USER_BALANCE,
                lastLoginAt: new Date()
            });
            
            console.log('✅ 调试用户信息已更新');
        } else {
            console.log('➕ 创建新的调试用户...');
            
            // 创建新用户
            const debugUser = new User({
                _id: DEBUG_USER_ID,
                email: DEBUG_USER_EMAIL,
                username: 'debug_user',
                fullName: 'Debug User',
                language: 'en',
                role: 'user',
                status: 'active',
                isDelete: false,
                balance: DEBUG_USER_BALANCE,
                lastLoginAt: new Date()
            });
            
            await debugUser.save();
            console.log('✅ 调试用户创建成功');
        }
        
        // 检查用户余额
        console.log('💰 检查用户余额...');
        let existingBalance = await UserBalance.findOne({ userId: DEBUG_USER_ID });
        
        if (existingBalance) {
            console.log(`💰 用户余额已存在: ${existingBalance.balance}，更新为: ${DEBUG_USER_BALANCE}`);
            await UserBalance.findOneAndUpdate(
                { userId: DEBUG_USER_ID },
                { balance: DEBUG_USER_BALANCE }
            );
        } else {
            console.log('➕ 创建用户余额记录...');
            const debugBalance = new UserBalance({
                userId: DEBUG_USER_ID,
                balance: DEBUG_USER_BALANCE
            });
            await debugBalance.save();
        }
        
        console.log('✅ 用户余额设置完成');
        
        // 验证创建结果
        const finalUser = await User.findById(DEBUG_USER_ID);
        const finalBalance = await UserBalance.findOne({ userId: DEBUG_USER_ID });
        
        console.log('\n📋 调试用户信息:');
        console.log(`   ID: ${finalUser._id}`);
        console.log(`   邮箱: ${finalUser.email}`);
        console.log(`   用户名: ${finalUser.username}`);
        console.log(`   全名: ${finalUser.fullName}`);
        console.log(`   角色: ${finalUser.role}`);
        console.log(`   状态: ${finalUser.status}`);
        console.log(`   余额: ${finalBalance.balance}`);
        console.log(`   创建时间: ${finalUser.createdAt}`);
        console.log(`   更新时间: ${finalUser.updatedAt}`);
        
        return {
            userId: DEBUG_USER_ID.toString(),
            email: DEBUG_USER_EMAIL,
            balance: DEBUG_USER_BALANCE
        };
        
    } catch (error) {
        console.error('❌ 创建调试用户失败:', error);
        throw error;
    }
}

/**
 * 主函数
 */
async function main() {
    try {
        // 连接数据库
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/ChatAdvisor_test';
        console.log(`🔗 连接数据库: ${mongoUri}`);
        
        await mongoose.connect(mongoUri);
        console.log('✅ 数据库连接成功');
        
        // 创建调试用户
        const debugUser = await createDebugUser();
        
        console.log('\n🎉 调试用户创建/更新完成！');
        console.log('\n💡 使用说明:');
        console.log('1. 调试Token现在可以使用这个真实的用户ID');
        console.log('2. 用户拥有大量余额，可以进行充分的API测试');
        console.log('3. 用户信息已设置为开发环境友好的默认值');
        console.log('\n🔧 下一步:');
        console.log('1. 更新verifyTokenHandler.ts中的调试用户ID');
        console.log('2. 重启服务器以应用更改');
        console.log('3. 使用调试Token测试API功能');
        
        // 输出用于更新代码的信息
        console.log('\n📝 代码更新信息:');
        console.log(`调试用户ID: ${debugUser.userId}`);
        console.log(`调试用户邮箱: ${debugUser.email}`);
        
    } catch (error) {
        console.error('❌ 脚本执行失败:', error);
        process.exit(1);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 数据库连接已关闭');
    }
}

// 运行脚本
main();
