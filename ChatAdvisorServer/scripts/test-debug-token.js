#!/usr/bin/env node

/**
 * 调试Token测试脚本
 * 用于验证调试Token功能是否正常工作
 */

const axios = require('axios');
const dotenv = require('dotenv');
const path = require('path');

// 加载开发环境配置
dotenv.config({ path: path.resolve(__dirname, '../.env.development') });
dotenv.config({ path: path.resolve(__dirname, '../.env') });

const DEBUG_TOKEN = process.env.DEBUG_TOKEN;
const PORT = process.env.PORT || 33001;
const BASE_URL = `http://localhost:${PORT}`;

console.log('🔧 调试Token测试脚本');
console.log('='.repeat(50));

if (!DEBUG_TOKEN) {
    console.error('❌ 错误：未找到DEBUG_TOKEN配置');
    console.log('请确保在.env.development文件中配置了DEBUG_TOKEN');
    process.exit(1);
}

console.log(`📍 服务器地址: ${BASE_URL}`);
console.log(`🔑 调试Token: ${DEBUG_TOKEN.substring(0, 20)}...`);
console.log('');

/**
 * 测试调试Token功能
 */
async function testDebugToken() {
    const testCases = [
        {
            name: '测试需要认证的API端点',
            url: '/api/chat',
            method: 'POST',
            data: { message: 'Hello, this is a test message' }
        },
        {
            name: '测试用户余额API',
            url: '/api/auth/userBalance',
            method: 'GET'
        },
        {
            name: '测试删除账户API',
            url: '/api/auth/deleteAccount',
            method: 'DELETE'
        }
    ];

    console.log('🧪 开始测试调试Token功能...\n');

    for (const testCase of testCases) {
        try {
            console.log(`📝 ${testCase.name}`);
            console.log(`   请求: ${testCase.method} ${testCase.url}`);

            const config = {
                method: testCase.method,
                url: `${BASE_URL}${testCase.url}`,
                headers: {
                    'Authorization': `Bearer ${DEBUG_TOKEN}`,
                    'Content-Type': 'application/json'
                },
                timeout: 5000
            };

            if (testCase.data) {
                config.data = testCase.data;
            }

            const response = await axios(config);
            
            console.log(`   ✅ 状态码: ${response.status}`);
            console.log(`   📄 响应: ${JSON.stringify(response.data, null, 2).substring(0, 200)}...`);
            
        } catch (error) {
            if (error.response) {
                console.log(`   ⚠️  状态码: ${error.response.status}`);
                console.log(`   📄 错误响应: ${JSON.stringify(error.response.data, null, 2)}`);
            } else if (error.code === 'ECONNREFUSED') {
                console.log(`   ❌ 连接失败: 服务器未启动 (${BASE_URL})`);
                console.log(`   💡 请先启动服务器: npm run dev`);
            } else {
                console.log(`   ❌ 请求失败: ${error.message}`);
            }
        }
        
        console.log('');
    }
}

/**
 * 测试无效Token
 */
async function testInvalidToken() {
    console.log('🔒 测试无效Token...\n');

    const invalidTokens = [
        'invalid_token',
        'DEBUG_invalid_token',
        'Bearer invalid_token',
        ''
    ];

    for (const invalidToken of invalidTokens) {
        try {
            console.log(`📝 测试无效Token: ${invalidToken || '(空Token)'}`);

            const response = await axios({
                method: 'GET',
                url: `${BASE_URL}/api/auth/userBalance`,
                headers: {
                    'Authorization': invalidToken ? `Bearer ${invalidToken}` : undefined,
                    'Content-Type': 'application/json'
                },
                timeout: 5000
            });

            console.log(`   ⚠️  意外成功: ${response.status}`);
            
        } catch (error) {
            if (error.response && (error.response.status === 401 || error.response.status === 403)) {
                console.log(`   ✅ 正确拒绝: ${error.response.status} - ${error.response.data.message || 'Unauthorized'}`);
            } else {
                console.log(`   ❌ 意外错误: ${error.message}`);
            }
        }
        
        console.log('');
    }
}

/**
 * 主函数
 */
async function main() {
    try {
        // 首先测试服务器是否可访问
        console.log('🔍 检查服务器状态...');
        await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
        console.log('✅ 服务器运行正常\n');

        // 测试调试Token
        await testDebugToken();

        // 测试无效Token
        await testInvalidToken();

        console.log('🎉 测试完成！');
        console.log('\n💡 使用提示:');
        console.log('1. 确保服务器在开发环境中运行 (NODE_ENV=development)');
        console.log('2. 调试Token只在开发/测试环境中生效');
        console.log('3. 查看服务器日志以确认调试Token使用记录');

    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.error('❌ 无法连接到服务器');
            console.log('💡 请确保服务器正在运行:');
            console.log('   cd ChatAdvisorServer');
            console.log('   npm run dev');
        } else {
            console.error('❌ 测试失败:', error.message);
        }
        process.exit(1);
    }
}

// 运行测试
main();
