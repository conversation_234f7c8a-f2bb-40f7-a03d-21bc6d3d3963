# 多AI服务提供商支持

## 概述

ChatAdvisorServer现在支持多个AI服务提供商，包括OpenAI、Google AI和Anthropic。系统提供了统一的接口来管理和使用不同的AI服务。

## 支持的提供商

### 1. OpenAI
- **提供商标识**: `openai`
- **默认API端点**: `https://api.openai.com/v1`
- **API密钥格式**: `sk-*` 或 `xai-*`
- **支持的模型**: GPT-4o, GPT-4o-mini, GPT-4-turbo, GPT-3.5-turbo等

### 2. Google AI (Gemini)
- **提供商标识**: `google`
- **默认API端点**: `https://generativelanguage.googleapis.com`
- **API密钥格式**: `AIza*`
- **支持的模型**: gemini-1.5-pro, gemini-1.5-flash, gemini-1.0-pro等

### 3. Anthropic (<PERSON>)
- **提供商标识**: `anthropic`
- **默认API端点**: `https://api.anthropic.com`
- **API密钥格式**: `sk-ant-*`
- **支持的模型**: claude-3-5-sonnet, claude-3-opus, claude-3-haiku等

## 配置管理

### 通过Admin界面配置

1. 访问管理后台：`http://localhost:33001/admin`
2. 导航到 "系统管理" > "AI配置管理"
3. 点击 "新增配置" 创建新的AI服务配置
4. 选择对应的服务提供商
5. 填写API密钥和其他配置信息
6. 测试连接确保配置正确
7. 设置为默认配置（可选）

### 配置字段说明

- **配置名称**: 便于识别的配置名称
- **描述**: 配置的详细说明
- **服务提供商**: 选择AI服务提供商（openai/google/anthropic）
- **API端点**: 服务的API基础URL
- **API密钥**: 对应提供商的API密钥
- **最大重试次数**: API调用失败时的重试次数
- **超时时间**: API调用的超时时间（毫秒）
- **代理配置**: 可选的代理设置
- **速率限制**: 每分钟请求数和令牌数限制

## 使用方式

### 自动选择

系统会自动选择当前启用的配置，或回退到默认配置。聊天接口会透明地使用配置的AI服务提供商。

### 手动切换

通过管理界面可以：
1. 启用/禁用特定配置
2. 设置默认配置
3. 切换当前使用的配置

## API兼容性

所有AI服务提供商都通过统一的接口提供服务，确保：
- 相同的聊天API格式
- 统一的流式响应格式
- 一致的错误处理
- 兼容的消息格式

## 消息格式转换

系统自动处理不同提供商之间的消息格式差异：

### OpenAI格式
```json
{
  "role": "user|assistant|system",
  "content": "消息内容"
}
```

### Google AI格式
```json
{
  "role": "user|model",
  "parts": [{"text": "消息内容"}]
}
```

### Anthropic格式
```json
{
  "role": "user|assistant",
  "content": "消息内容"
}
```

## 错误处理

系统提供统一的错误处理机制：
- 自动重试失败的请求
- 提供商特定的错误信息转换
- 详细的日志记录
- 优雅的降级处理

## 性能优化

- **客户端缓存**: 缓存AI客户端实例以提高性能
- **配置缓存**: 缓存配置信息减少数据库查询
- **连接池**: 复用HTTP连接
- **智能清理**: 定期清理过期的缓存项

## 安全性

- **API密钥加密**: 所有API密钥使用AES-256-GCM加密存储
- **权限控制**: 只有管理员可以管理AI配置
- **敏感信息隐藏**: 前端显示时隐藏API密钥
- **审计日志**: 记录所有配置变更操作

## 监控和统计

- **使用统计**: 跟踪每个配置的使用情况
- **性能监控**: 记录响应时间和成功率
- **错误追踪**: 详细的错误日志和统计
- **成本分析**: 按提供商和模型统计使用成本

## 故障排除

### 常见问题

1. **API密钥无效**
   - 检查密钥格式是否正确
   - 确认密钥有效期和权限

2. **连接超时**
   - 检查网络连接
   - 调整超时设置
   - 配置代理（如需要）

3. **模型不可用**
   - 确认模型名称正确
   - 检查提供商的模型可用性
   - 更新模型列表

### 日志查看

```bash
# 查看AI服务相关日志
pm2 logs chat-advisor-server-v2 | grep -i "ai\|openai\|google\|anthropic"
```

## 开发指南

### 添加新的提供商

1. 创建新的客户端工厂类
2. 实现IAIClientFactory接口
3. 在AIClientManager中注册工厂
4. 更新前端配置选项
5. 添加相应的测试

### 自定义消息处理

可以通过扩展MessageConverter类来支持特殊的消息格式转换需求。

## 版本兼容性

- Node.js >= 20.0.0
- 支持的SDK版本：
  - @google/generative-ai: ^0.24.1
  - @anthropic-ai/sdk: ^0.59.0
  - openai: ^5.11.0
