# 开发环境配置
# 注意：此文件用于本地开发

# 服务器配置
NODE_ENV=development
PORT=33001

# 数据库配置 - 开发环境使用测试数据库
MONGODB_URI=mongodb://localhost:27017/ChatAdvisor_test

# 开发环境特定配置

# 应用配置 - 开发环境
BASE_URL=http://localhost:3000
APP_VERSION=1.0.0-dev
NEED_TO_ENCRYPT=0

# CORS配置 - 开发环境允许本地访问
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:34001,http://localhost:54001,https://admin.sanva.top

# 管理员密码配置（开发环境使用简单密码）
ADMIN_PASSWORD=admin123
MODERATOR_PASSWORD=moderator123

# 代理配置 - 仅在开发环境启用
HTTP_PROXY=http://127.0.0.1:7890
HTTPS_PROXY=http://127.0.0.1:7890

# 开发环境标识
ENV_LABEL=development

# 调试Token配置 - 仅在开发/测试环境使用
# 警告：此Token可绕过正常身份验证，绝不能在生产环境使用
DEBUG_TOKEN=DEBUG_a7f3e9d2c8b1f4e6a9c2d5f8b3e7a1c4d6f9b2e5a8c1d4f7b0e3a6c9d2f5e8b1
