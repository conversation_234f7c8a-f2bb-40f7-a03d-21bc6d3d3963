/**
 * 主应用程序文件
 * 使用新的数据库架构和后台管理系统
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import compression from 'compression';
import path from 'path';
import { engine } from 'express-handlebars';

import { initializeDatabase, getDatabaseInitializer } from './database/connection';
import { logger } from './utils/logger';
import { configureRoutes } from './routers';
import adminRoutes from './admin/routes';
import errorHandler from './middlewares/errorHandler';

// 创建Express应用
const app = express();

// 安全中间件
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
            imgSrc: ["'self'", "data:", "https:"],
            fontSrc: ["'self'", "https://cdn.jsdelivr.net"]
        }
    }
}));

// CORS配置 - 修复CORS问题，添加前端地址和缺失的域名
const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
    'http://localhost:3000',
    'http://localhost:54001',  // admin-frontend 本地开发端口
    'https://advisor.sanva.tk',
    'https://advisor.sanva.top',
    'https://admin.sanva.top',
    'https://admin.sanva.tk'   // admin-frontend 生产环境域名
];
app.use(cors({
    origin: (origin, callback) => {
        // 如果没有origin（同源请求），也允许
        if (!origin) {
            return callback(null, true);
        }
        // 检查origin是否在允许列表中
        if (allowedOrigins.includes(origin)) {
            return callback(null, true);
        }
        // 如果是localhost（开发环境），也允许
        if (origin.includes('localhost')) {
            return callback(null, true);
        }
        return callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'App-Version', 'OS-Version', 'Device-Model', 'Time-Zone', 'Network-Type', 'Screen-Resolution', 'Device-Orientation', 'local'],
    optionsSuccessStatus: 200 // 确保OPTIONS请求返回200而不是204
}));

// 压缩响应（为SSE/流式接口禁用压缩，避免中间件或代理缓冲导致流失效）
app.use(compression({
    filter: (req, res) => {
        // 路径命中聊天流接口则禁用压缩
        const url = req.originalUrl || req.url || '';
        if (url.includes('/chat')) return false;
        // 如果已经设置为SSE类型也禁用压缩
        const contentType = res.getHeader('Content-Type');
        if (typeof contentType === 'string' && contentType.includes('text/event-stream')) {
            return false;
        }
        // 其余情况使用默认过滤策略
        // @ts-ignore
        return compression.filter(req, res);
    }
}));

// 请求体解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务和模板引擎配置
// 生产环境中, __dirname 指向 dist/src, 因此需要向上回溯两层到项目根目录
const projectRoot = path.resolve(__dirname, '../../');
app.use(express.static(path.join(projectRoot, 'public')));
app.engine('handlebars', engine());
app.set('view engine', 'handlebars');
// {{ AURA-X: Modify - 修复生产环境views目录路径，优先使用构建后的dist/src/views. Approval: mcp-feedback-enhanced(ID:20250806160). }}
const viewsPath = process.env.NODE_ENV === 'production'
    ? path.join(projectRoot, 'dist/src/views')  // 生产环境使用构建后的views
    : path.join(projectRoot, 'src/views');      // 开发环境使用源码views
app.set('views', viewsPath);

// 速率限制
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 限制每个IP 15分钟内最多100个请求
    message: {
        error: 'Too many requests from this IP, please try again later.'
    }
});
app.use('/api/', limiter);

// 管理后台速率限制（更严格）
const adminLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 50,
    message: {
        error: 'Too many admin requests from this IP, please try again later.'
    }
});
app.use('/api/admin/', adminLimiter);

// API路由
app.use('/api/admin', adminRoutes);

// 配置其他路由
configureRoutes(app);

// 健康检查端点
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// 错误处理中间件
app.use(errorHandler);

// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Route not found'
    });
});

/**
 * 启动应用程序
 */
async function startApp(): Promise<void> {
    try {
        // 初始化数据库
        logger.info('Initializing database...');
        await initializeDatabase();
        logger.info('Database initialized successfully');

        // 启动服务器
        const port = process.env.PORT || 3000;
        app.listen(port, () => {
            logger.info(`Server is running on port ${port}`);
            logger.info(`Admin panel available at http://localhost:${port}/admin`);
        });

    } catch (error) {
        logger.error('Failed to start application:', error);
        process.exit(1);
    }
}

// 优雅关闭
process.on('SIGTERM', async () => {
    logger.info('SIGTERM received, shutting down gracefully');
    
    try {
        const initializer = getDatabaseInitializer();
        await initializer.shutdown();
        logger.info('Database connection closed');
    } catch (error) {
        logger.error('Error during shutdown:', error);
    }
    
    process.exit(0);
});

process.on('SIGINT', async () => {
    logger.info('SIGINT received, shutting down gracefully');
    
    try {
        const initializer = getDatabaseInitializer();
        await initializer.shutdown();
        logger.info('Database connection closed');
    } catch (error) {
        logger.error('Error during shutdown:', error);
    }
    
    process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// 启动应用
if (require.main === module) {
    startApp();
}

export default app;
