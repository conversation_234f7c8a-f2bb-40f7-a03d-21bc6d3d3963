import mongoose, { Document, Model, Schema } from 'mongoose';

export interface IAIServiceModel extends Document {
    configId: mongoose.Types.ObjectId;
    modelName: string;
    displayName: string;
    description?: string;
    maxTokens: number;
    supportedFeatures: string[];
    pricing: {
        inputPrice: number;
        outputPrice: number;
        currency: string;
    };
    parameters: {
        temperature?: number;
        maxTokens?: number;
        topP?: number;
        frequencyPenalty?: number;
        presencePenalty?: number;
    };
    // 存储模型的完整原始信息
    modelInfo?: any;
    // 标记价格是否被手动修改过
    isPricingCustomized: boolean;
    // 模型来源：sync(同步获取) | manual(手动添加)
    source: 'sync' | 'manual';
    isActive: boolean;
    sortOrder: number;
    createdAt: Date;
    updatedAt: Date;
}

const aiServiceModelSchema = new Schema({
    configId: {
        type: Schema.Types.ObjectId,
        ref: 'AIServiceConfig',
        required: true
    },
    modelName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    displayName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
    },
    description: {
        type: String,
        trim: true,
        maxlength: 1000
    },
    maxTokens: {
        type: Number,
        required: true,
        min: 1,
        max: 1000000,
        default: 4096
    },
    supportedFeatures: [{
        type: String,
        enum: ['text', 'image', 'function_calling', 'streaming', 'vision', 'audio']
    }],
    pricing: {
        inputPrice: {
            type: Number,
            required: true,
            min: 0,
            default: 0
        },
        outputPrice: {
            type: Number,
            required: true,
            min: 0,
            default: 0
        },
        currency: {
            type: String,
            required: true,
            default: 'USD',
            maxlength: 3
        }
    },
    parameters: {
        temperature: {
            type: Number,
            min: 0,
            max: 2,
            default: 0.7
        },
        maxTokens: {
            type: Number,
            min: 1,
            max: 100000
        },
        topP: {
            type: Number,
            min: 0,
            max: 1,
            default: 1
        },
        frequencyPenalty: {
            type: Number,
            min: -2,
            max: 2,
            default: 0
        },
        presencePenalty: {
            type: Number,
            min: -2,
            max: 2,
            default: 0
        }
    },
    // 存储模型的完整原始信息
    modelInfo: {
        type: Schema.Types.Mixed,
        default: null
    },
    // 标记价格是否被手动修改过
    isPricingCustomized: {
        type: Boolean,
        required: true,
        default: false
    },
    // 模型来源：sync(同步获取) | manual(手动添加)
    source: {
        type: String,
        required: true,
        enum: ['sync', 'manual'],
        default: 'sync'
    },
    isActive: {
        type: Boolean,
        required: true,
        default: true
    },
    sortOrder: {
        type: Number,
        required: true,
        default: 0
    }
}, {
    timestamps: true,
    versionKey: false
});

// 复合索引
aiServiceModelSchema.index({ configId: 1, modelName: 1 }, { unique: true });
aiServiceModelSchema.index({ configId: 1, isActive: 1, sortOrder: 1 });
aiServiceModelSchema.index({ supportedFeatures: 1 });

// 虚拟字段：格式化的定价信息
aiServiceModelSchema.virtual('formattedPricing').get(function() {
    return {
        input: `${this.pricing.inputPrice} ${this.pricing.currency}/1K tokens`,
        output: `${this.pricing.outputPrice} ${this.pricing.currency}/1K tokens`
    };
});

const modelName = 'AIServiceModel';

const AIServiceModel: Model<IAIServiceModel> = mongoose.models[modelName] || 
    mongoose.model<IAIServiceModel>(modelName, aiServiceModelSchema);

export default AIServiceModel;
