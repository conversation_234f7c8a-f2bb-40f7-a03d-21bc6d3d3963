import AIServiceConfig, { IAIServiceConfig } from '../models/AIServiceConfig';
import AIServiceModel, { IAIServiceModel } from '../models/AIServiceModel';
import AIConfigUsageLog from '../models/AIConfigUsageLog';
import { encryptApiKey, decryptApiKey } from '../utils/encryption';
import { logger } from '../business/logger';
import AIConfigCache from './AIConfigCache';
import OpenAIClientFactory from './OpenAIClientFactory';
import { aiClientManager } from './ai/AIClientManager';
import axios from 'axios';

export interface TestResult {
    success: boolean;
    models?: string[];
    error?: string;
    responseTime?: number;
}

export interface UsageStats {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    totalTokens: number;
    totalCost: number;
    averageResponseTime: number;
}

class AIConfigManager {
    /**
     * 获取当前活跃的默认配置
     */
    async getActiveConfig(): Promise<IAIServiceConfig> {
        // 先尝试从缓存获取
        let config = await AIConfigCache.getCachedDefaultConfig();
        
        if (!config) {
            // 从数据库获取
            config = await AIServiceConfig.findOne({ 
                isDefault: true, 
                isActive: true 
            }).select('+apiKey');
            
            if (!config) {
                throw new Error('未找到默认的AI服务配置');
            }
            
            // 缓存配置
            await AIConfigCache.setCachedConfig(config);
        }
        
        return config;
    }

    /**
     * 获取指定配置
     */
    async getConfigById(id: string): Promise<IAIServiceConfig> {
        // 先尝试从缓存获取
        let config = await AIConfigCache.getCachedConfig(id);
        
        if (!config) {
            // 从数据库获取
            config = await AIServiceConfig.findById(id).select('+apiKey');
            if (!config) {
                throw new Error(`AI服务配置不存在: ${id}`);
            }
            
            // 缓存配置
            await AIConfigCache.setCachedConfig(config);
        }
        
        return config;
    }

    /**
     * 获取配置列表
     */
    async getConfigs(options: {
        page?: number;
        limit?: number;
        search?: string;
        provider?: string;
        isActive?: boolean;
    } = {}): Promise<{ configs: IAIServiceConfig[]; total: number; page: number; limit: number }> {
        const { page = 1, limit = 10, search, provider, isActive } = options;
        
        const query: any = {};
        
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }
        
        if (provider) {
            query.provider = provider;
        }
        
        if (typeof isActive === 'boolean') {
            query.isActive = isActive;
        }
        
        const total = await AIServiceConfig.countDocuments(query);
        const configs = await AIServiceConfig.find(query)
            .sort({ isDefault: -1, createdAt: -1 })
            .skip((page - 1) * limit)
            .limit(limit)
            .populate('createdBy', 'username email');
        
        return { configs, total, page, limit };
    }

    /**
     * 创建配置
     */
    async createConfig(configData: Partial<IAIServiceConfig>, createdBy: string): Promise<IAIServiceConfig> {
        // 加密API密钥
        if (configData.apiKey) {
            configData.apiKey = encryptApiKey(configData.apiKey);
        }
        
        // 设置创建者
        configData.createdBy = createdBy as any;
        
        const config = new AIServiceConfig(configData);
        await config.save();
        
        logger.info(`创建AI服务配置: ${config.name} (${config._id})`);
        
        // 清除相关缓存
        await AIConfigCache.clearConfigCache();
        
        return config;
    }

    /**
     * 更新配置
     */
    async updateConfig(id: string, updates: Partial<IAIServiceConfig>): Promise<IAIServiceConfig> {
        // 如果更新API密钥，需要重新加密
        if (updates.apiKey) {
            updates.apiKey = encryptApiKey(updates.apiKey);
        }
        
        const config = await AIServiceConfig.findByIdAndUpdate(
            id,
            updates,
            { new: true, runValidators: true }
        ).select('+apiKey');
        
        if (!config) {
            throw new Error(`AI服务配置不存在: ${id}`);
        }
        
        logger.info(`更新AI服务配置: ${config.name} (${config._id})`);
        
        // 清除相关缓存
        await AIConfigCache.clearConfigCache(id);
        OpenAIClientFactory.clearClientCache(id);
        
        return config;
    }

    /**
     * 删除配置
     */
    async deleteConfig(id: string): Promise<boolean> {
        const config = await AIServiceConfig.findById(id);
        if (!config) {
            throw new Error(`AI服务配置不存在: ${id}`);
        }
        
        if (config.isDefault) {
            throw new Error('不能删除默认配置');
        }
        
        // 删除相关模型
        await AIServiceModel.deleteMany({ configId: id });
        
        // 删除配置
        await AIServiceConfig.findByIdAndDelete(id);
        
        logger.info(`删除AI服务配置: ${config.name} (${id})`);
        
        // 清除相关缓存
        await AIConfigCache.clearConfigCache(id);
        await AIConfigCache.clearModelCache(id);
        OpenAIClientFactory.clearClientCache(id);
        
        return true;
    }

    /**
     * 测试配置连接
     */
    async testConfig(id: string): Promise<TestResult> {
        const startTime = Date.now();

        try {
            const config = await this.getConfigById(id);

            // 使用新的AI客户端管理器进行配置验证
            const isValid = await aiClientManager.validateConfig(config);

            const responseTime = Date.now() - startTime;

            if (isValid) {
                // 更新最后使用时间
                await AIServiceConfig.findByIdAndUpdate(id, { lastUsedAt: new Date() });

                return {
                    success: true,
                    // message: `${config.provider} 配置连接成功`,
                    responseTime
                };
            } else {
                return {
                    success: false,
                    error: `${config.provider} 配置验证失败`,
                    responseTime
                };
            }
        } catch (error) {
            logger.error(`测试AI配置连接失败: ${id}, 错误: ${error.message}`);
            return {
                success: false,
                error: error.message,
                responseTime: Date.now() - startTime
            };
        }
    }

    /**
     * 设置默认配置
     */
    async setDefaultConfig(id: string): Promise<boolean> {
        const config = await AIServiceConfig.findById(id);
        if (!config) {
            throw new Error(`AI服务配置不存在: ${id}`);
        }
        
        if (!config.isActive) {
            throw new Error('不能将禁用的配置设为默认');
        }
        
        // 取消其他默认配置
        await AIServiceConfig.updateMany(
            { _id: { $ne: id } },
            { isDefault: false }
        );
        
        // 设置新的默认配置
        await AIServiceConfig.findByIdAndUpdate(id, { isDefault: true });
        
        logger.info(`设置默认AI配置: ${config.name} (${id})`);
        
        // 清除缓存
        await AIConfigCache.clearConfigCache();
        OpenAIClientFactory.clearClientCache();
        
        return true;
    }

    /**
     * 同步模型列表
     */
    async syncModels(
        configId: string,
        options?: { modelsOverride?: string[] }
    ): Promise<IAIServiceModel[]> {
        const config = await this.getConfigById(configId);
        let testResult: { success: boolean; models?: string[]; modelsInfo?: any[]; error?: string };

        const modelsOverride = options?.modelsOverride;
        if (Array.isArray(modelsOverride) && modelsOverride.length > 0) {
            logger.info(`使用客户端提供的本地模型列表进行同步: ${modelsOverride.length} 个`);
            testResult = { success: true, models: modelsOverride };
        } else {
            // 统一走 OpenAI 兼容的 /v1/models 列表逻辑
            testResult = await OpenAIClientFactory.testConnection(config);
        }

        if (!testResult.success || !testResult.models) {
            throw new Error(`无法获取模型列表: ${testResult.error}`);
        }

        // 获取现有模型，保留已手动修改价格的模型
        const existingModels = await AIServiceModel.find({ configId }).lean();
        const existingModelMap = new Map();
        existingModels.forEach(model => {
            existingModelMap.set(model.modelName, model);
        });

        // 删除现有模型
        await AIServiceModel.deleteMany({ configId });

        // 默认价格：统一以每3000字符计价，货币统一为 USD
        // 维持数值为 5/3000，对应 $5 / 3000 字符
        const DEFAULT_PRICE = 5 / 3000;

        // 创建新模型
        const models: IAIServiceModel[] = [];
        for (let i = 0; i < testResult.models.length; i++) {
            const modelName = testResult.models[i];
            // 获取对应的模型详细信息
            const modelInfo = testResult.modelsInfo ?
                testResult.modelsInfo.find(info => info.id === modelName) : null;

            // 检查是否存在已有的模型配置
            const existingModel = existingModelMap.get(modelName);
            const isPricingCustomized = existingModel?.isPricingCustomized || false;
            const isActive = existingModel?.isActive || false; // 保留原有的启用状态

            // 如果价格已被手动修改，保留原价格；否则使用默认价格
            const pricing = isPricingCustomized && existingModel ? {
                inputPrice: existingModel.pricing.inputPrice,
                outputPrice: existingModel.pricing.outputPrice,
                currency: 'USD'
            } : {
                inputPrice: DEFAULT_PRICE,
                outputPrice: DEFAULT_PRICE,
                currency: 'USD'
            };

            const model = new AIServiceModel({
                configId,
                modelName,
                displayName: modelName,
                maxTokens: 4096, // 默认值
                supportedFeatures: ['text'],
                pricing,
                modelInfo: modelInfo, // 存储完整的模型信息
                isPricingCustomized, // 保留自定义标记
                source: 'sync', // 标记为同步获取
                isActive, // 保留原有的启用状态，新模型默认为false
                sortOrder: i
            });
            await model.save();
            models.push(model);
        }

        const newModelsCount = models.filter(m => !existingModelMap.has(m.modelName)).length;
        const preservedModelsCount = models.length - newModelsCount;
        logger.info(`同步AI模型列表: ${config.name}, 总计${models.length}个模型（新增${newModelsCount}个，保留${preservedModelsCount}个），默认价格: ${DEFAULT_PRICE.toFixed(6)} USD/字符，新模型默认不启用`);

        // 清除模型缓存
        await AIConfigCache.clearModelCache(configId);
        
        return models;
    }

    /**
     * 服务器端判断是否为本地地址
     */
    private isLocalAddress(url: string): boolean {
        try {
            const { hostname } = new URL(url);
            const h = hostname.toLowerCase();
            return h === 'localhost' || h === '127.0.0.1' || h === '::1' ||
                h.startsWith('10.') || h.startsWith('192.168.') || h.startsWith('172.16.') || h.endsWith('.local');
        } catch {
            return false;
        }
    }

    // （移除本地 /api/models 解析逻辑）

    /**
     * 记录使用日志
     */
    async logUsage(data: {
        configId: string;
        modelId: string;
        userId: string;
        requestId: string;
        tokensUsed: number;
        cost: number;
        responseTime: number;
        success: boolean;
        errorMessage?: string;
    }): Promise<void> {
        const log = new AIConfigUsageLog(data);
        await log.save();
        
        // 更新配置的最后使用时间
        await AIServiceConfig.findByIdAndUpdate(data.configId, { lastUsedAt: new Date() });
    }

    /**
     * 获取使用统计
     */
    async getUsageStats(configId: string, startDate?: Date, endDate?: Date): Promise<UsageStats> {
        const query: any = { configId };
        
        if (startDate || endDate) {
            query.timestamp = {};
            if (startDate) query.timestamp.$gte = startDate;
            if (endDate) query.timestamp.$lte = endDate;
        }
        
        const logs = await AIConfigUsageLog.find(query);
        
        const stats: UsageStats = {
            totalRequests: logs.length,
            successfulRequests: logs.filter(log => log.success).length,
            failedRequests: logs.filter(log => !log.success).length,
            totalTokens: logs.reduce((sum, log) => sum + log.tokensUsed, 0),
            totalCost: logs.reduce((sum, log) => sum + log.cost, 0),
            averageResponseTime: logs.length > 0 
                ? logs.reduce((sum, log) => sum + log.responseTime, 0) / logs.length 
                : 0
        };
        
        return stats;
    }

    /**
     * 手动添加模型
     */
    async addManualModels(configId: string, modelsData: any[]): Promise<IAIServiceModel[]> {
        const config = await this.getConfigById(configId);
        const addedModels: IAIServiceModel[] = [];

        // 获取现有模型名称，用于重复检查
        const existingModels = await AIServiceModel.find({ configId }).select('modelName');
        const existingModelNames = new Set(existingModels.map(m => m.modelName));

        // 获取当前最大排序值
        const maxSortOrder = await AIServiceModel.findOne({ configId })
            .sort({ sortOrder: -1 })
            .select('sortOrder');
        let currentSortOrder = maxSortOrder ? maxSortOrder.sortOrder + 1 : 0;

        for (const modelData of modelsData) {
            const { modelName, displayName, description, maxTokens, supportedFeatures, pricing, parameters } = modelData;

            // 验证必填字段
            if (!modelName || typeof modelName !== 'string') {
                throw new Error('模型名称是必填字段');
            }

            // 检查模型名称是否重复
            if (existingModelNames.has(modelName)) {
                throw new Error(`模型 "${modelName}" 已存在`);
            }

            // 创建新模型
            const model = new AIServiceModel({
                configId,
                modelName: modelName.trim(),
                displayName: displayName?.trim() || modelName.trim(),
                description: description?.trim() || '',
                maxTokens: maxTokens || 4096,
                supportedFeatures: Array.isArray(supportedFeatures) ? supportedFeatures : ['text'],
                pricing: {
                    inputPrice: pricing?.inputPrice || 0,
                    outputPrice: pricing?.outputPrice || 0,
                    currency: pricing?.currency || 'USD'
                },
                parameters: {
                    temperature: parameters?.temperature || 0.7,
                    maxTokens: parameters?.maxTokens,
                    topP: parameters?.topP || 1,
                    frequencyPenalty: parameters?.frequencyPenalty || 0,
                    presencePenalty: parameters?.presencePenalty || 0
                },
                source: 'manual', // 标记为手动添加
                isPricingCustomized: false,
                isActive: true, // 手动添加的模型默认启用
                sortOrder: currentSortOrder++
            });

            await model.save();
            addedModels.push(model);
            existingModelNames.add(modelName); // 添加到已存在集合中，避免批量添加时重复
        }

        logger.info(`手动添加模型: ${config.name}, 添加了 ${addedModels.length} 个模型`);

        // 清除模型缓存
        await AIConfigCache.clearModelCache(configId);

        return addedModels;
    }

    /**
     * 删除手动添加的模型
     */
    async deleteManualModel(configId: string, modelId: string): Promise<boolean> {
        const config = await this.getConfigById(configId);

        // 查找模型并验证是否为手动添加
        const model = await AIServiceModel.findOne({
            _id: modelId,
            configId,
            source: 'manual'
        });

        if (!model) {
            throw new Error('模型不存在或不是手动添加的模型');
        }

        await AIServiceModel.deleteOne({ _id: modelId });

        logger.info(`删除手动模型: ${config.name} - ${model.modelName}`);

        // 清除模型缓存
        await AIConfigCache.clearModelCache(configId);

        return true;
    }
}

// 单例模式
export default new AIConfigManager();
