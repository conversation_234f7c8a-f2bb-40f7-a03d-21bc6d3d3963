/**
 * OpenAI客户端工厂 - 重构版本
 * 符合统一的AI客户端接口
 */

import OpenAI from 'openai';
import * as undici from 'undici';
import { IAIClient, IAIClientFactory, IAIMessage, IAIStreamChunk, IAICompletionOptions, AIClientError } from '../interfaces/IAIClient';
import { MessageConverter } from '../utils/messageConverter';
import { logger } from '../../../utils/logger';
import { IAIServiceConfig } from '../../../models/AIServiceConfig';
import { decryptApiKey } from '../../../utils/encryption';
import { getProxyConfig } from '../../../config/proxyConfig';

/**
 * OpenAI客户端实现
 */
class OpenAIClient implements IAIClient {
    public readonly provider = 'openai';

    constructor(
        public readonly configId: string,
        public readonly modelName: string,
        private openai: OpenAI
    ) {}

    async *createChatCompletionStream(
        messages: IAIMessage[], 
        options?: IAICompletionOptions
    ): AsyncIterable<IAIStreamChunk> {
        try {
            logger.debug(`OpenAI 开始流式聊天: ${this.modelName}`);
            
            // 转换消息格式
            const openaiMessages = MessageConverter.toOpenAI(messages);
            
            // 配置请求参数
            const requestParams: OpenAI.Chat.ChatCompletionCreateParams = {
                model: this.modelName,
                messages: openaiMessages,
                stream: true,
                temperature: options?.temperature ?? 0.7,
                max_tokens: options?.maxTokens ?? 2048,
                ...options // 允许传递其他OpenAI特定参数
            };

            // 发送流式请求
            const stream = await this.openai.chat.completions.create(requestParams);

            let totalTokens = 0;
            for await (const chunk of stream as any) {
                const delta = chunk.choices?.[0]?.delta;
                if (delta?.content) {
                    totalTokens += delta.content.length; // 简单估算
                    yield {
                        content: delta.content,
                        done: false
                    };
                }
                
                // 检查是否完成
                if (chunk.choices?.[0]?.finish_reason) {
                    yield {
                        content: '',
                        done: true,
                        usage: {
                            promptTokens: chunk.usage?.prompt_tokens,
                            completionTokens: chunk.usage?.completion_tokens,
                            totalTokens: chunk.usage?.total_tokens || totalTokens
                        }
                    };
                    break;
                }
            }

            logger.debug(`OpenAI 流式聊天完成: ${totalTokens} tokens`);

        } catch (error: any) {
            logger.error('OpenAI 流式聊天错误:', error);
            
            yield {
                content: '',
                done: true,
                error: error.message || 'OpenAI 服务错误'
            };
            
            throw new AIClientError(
                `OpenAI 聊天失败: ${error.message}`,
                this.provider,
                error.code,
                error.status
            );
        }
    }

    async testConnection(): Promise<boolean> {
        try {
            logger.debug('测试 OpenAI 连接');
            
            // 发送简单的测试消息
            const response = await this.openai.chat.completions.create({
                model: this.modelName,
                messages: [{ role: 'user', content: 'Hello' }],
                max_tokens: 10
            });
            
            logger.debug('OpenAI 连接测试成功');
            return !!response.choices?.[0]?.message?.content;
        } catch (error: any) {
            logger.error('OpenAI 连接测试失败:', error);
            return false;
        }
    }

    async getModels(): Promise<string[]> {
        try {
            const response = await this.openai.models.list();
            return response.data
                .filter(model => model.id.includes('gpt') || model.id.includes('chat'))
                .map(model => model.id)
                .sort();
        } catch (error: any) {
            logger.error('获取 OpenAI 模型列表失败:', error);
            // 返回常用模型作为备选
            return [
                'gpt-4o',
                'gpt-4o-mini',
                'gpt-4-turbo',
                'gpt-4',
                'gpt-3.5-turbo'
            ];
        }
    }

    dispose(): void {
        // OpenAI SDK 不需要显式释放资源
        logger.debug('OpenAI 客户端已释放');
    }
}

/**
 * OpenAI客户端工厂
 */
export class OpenAIClientFactory implements IAIClientFactory {
    public readonly provider = 'openai';

    async createClient(config: IAIServiceConfig, modelName: string): Promise<IAIClient> {
        try {
            logger.info(`创建 OpenAI 客户端: ${config.name} - ${modelName}`);
            
            // 解密API密钥
            const apiKey = decryptApiKey(config.apiKey);
            
            logger.info('=== 创建OpenAI客户端 ===');
            logger.info(`配置名称: ${config.name}`);
            logger.info(`Base URL: ${config.baseURL}`);
            logger.info(`API Key: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 4)}`);
            logger.info(`提供商: ${config.provider}`);
            logger.info(`超时时间: ${config.timeout}ms`);
            logger.info(`最大重试: ${config.maxRetries}`);

            // 配置代理支持
            let fetchOptions: any = {};

            // 优先使用配置中的代理设置
            if (config.proxyConfig?.enabled && config.proxyConfig.url) {
                logger.info(`使用配置代理: ${config.proxyConfig.url}`);
                const proxyAgent = new undici.ProxyAgent(config.proxyConfig.url);
                fetchOptions = {
                    dispatcher: proxyAgent,
                };
            } else {
                // 使用环境感知的代理配置
                const proxyConfig = getProxyConfig();
                if (proxyConfig.enabled && proxyConfig.url) {
                    logger.info(`使用环境代理 (${process.env.NODE_ENV}): ${proxyConfig.url}`);
                    const proxyAgent = new undici.ProxyAgent(proxyConfig.url);
                    fetchOptions = {
                        dispatcher: proxyAgent,
                    };
                } else {
                    logger.info(`代理已禁用 (环境: ${process.env.NODE_ENV || 'development'})`);
                }
            }

            // 归一化 BaseURL，兼容 OpenAI 风格聚合网关（自动补全 /v1）
            const normalizeBaseURL = (raw: string): string => {
                try {
                    const u = new URL(raw);
                    const path = u.pathname || '/';
                    if (path === '/' || path === '') {
                        u.pathname = '/v1';
                    } else if (!/\/v1(\/|$)/.test(path)) {
                        u.pathname = path.replace(/\/$/, '') + '/v1';
                    }
                    return u.toString().replace(/\/$/, '');
                } catch {
                    return raw.endsWith('/v1') ? raw.replace(/\/$/, '') : raw.replace(/\/$/, '') + '/v1';
                }
            };

            const effectiveBaseURL = normalizeBaseURL(config.baseURL);

            // 创建OpenAI实例
            const openai = new OpenAI({
                apiKey: apiKey,
                baseURL: effectiveBaseURL,
                timeout: config.timeout,
                maxRetries: config.maxRetries,
                fetchOptions: fetchOptions,
            });
            
            // 创建客户端
            const client = new OpenAIClient(
                config._id.toString(),
                modelName,
                openai
            );
            
            logger.info(`OpenAI 客户端创建成功: ${config.name}`);
            return client;
            
        } catch (error: any) {
            logger.error('创建 OpenAI 客户端失败:', error);
            throw new AIClientError(
                `创建 OpenAI 客户端失败: ${error.message}`,
                this.provider
            );
        }
    }

    async validateConfig(config: IAIServiceConfig): Promise<boolean> {
        try {
            logger.debug(`验证 OpenAI 配置: ${config.name}`);
            
            // 检查必需字段
            if (!config.apiKey || !config.baseURL) {
                logger.error('OpenAI 配置缺少必需字段');
                return false;
            }

            // 解密并验证API密钥格式
            const apiKey = decryptApiKey(config.apiKey);
            // 放宽密钥前缀校验，兼容代理/聚合网关（例如 OneAPI、自建网关）
            if (!apiKey || apiKey.length < 8) {
                logger.error('OpenAI API 密钥格式无效');
                return false;
            }

            // 创建临时客户端测试连接（优先使用 models.list 更轻量、兼容度更高）
            const normalizeBaseURL = (raw: string): string => {
                try {
                    const u = new URL(raw);
                    const path = u.pathname || '/';
                    if (path === '/' || path === '') {
                        u.pathname = '/v1';
                    } else if (!/\/v1(\/|$)/.test(path)) {
                        u.pathname = path.replace(/\/$/, '') + '/v1';
                    }
                    return u.toString().replace(/\/$/, '');
                } catch {
                    return raw.endsWith('/v1') ? raw.replace(/\/$/, '') : raw.replace(/\/$/, '') + '/v1';
                }
            };

            const effectiveBaseURL = normalizeBaseURL(config.baseURL);

            const openai = new OpenAI({
                apiKey: apiKey,
                baseURL: effectiveBaseURL,
                timeout: config.timeout,
                maxRetries: Math.max(0, config.maxRetries || 0),
            });

            try {
                const modelsResp = await openai.models.list();
                const count = Array.isArray(modelsResp?.data) ? modelsResp.data.length : 0;
                logger.debug(`OpenAI 模型列表获取结果: ${count} 个`);
                if (count > 0) {
                    logger.debug('OpenAI 配置验证成功（通过 models.list）');
                    return true;
                }
            } catch (listErr: any) {
                logger.warn(`获取模型列表失败，尝试最小化 chat 测试: ${listErr?.message || listErr}`);
            }

            // 回退：进行一次最小化聊天测试（在部分网关上 models.list 可能未实现）
            try {
                const resp = await openai.chat.completions.create({
                    model: 'gpt-3.5-turbo',
                    messages: [{ role: 'user', content: 'ping' }],
                    max_tokens: 5
                } as any);
                const ok = !!resp?.choices?.[0]?.message?.content;
                if (ok) {
                    logger.debug('OpenAI 配置验证成功（通过 chat 最小化测试）');
                }
                return ok;
            } catch (chatErr: any) {
                logger.error('OpenAI 配置验证失败（chat 测试）:', chatErr);
                return false;
            }
            
        } catch (error: any) {
            logger.error('OpenAI 配置验证失败:', error);
            return false;
        }
    }

    getDefaultConfig(): Partial<IAIServiceConfig> {
        return {
            provider: 'openai',
            baseURL: 'https://api.openai.com/v1',
            maxRetries: 3,
            timeout: 60000,
            rateLimits: {
                requestsPerMinute: 60,
                tokensPerMinute: 100000
            }
        };
    }
}

export default OpenAIClientFactory;
