/**
 * Google AI客户端工厂
 */

import { GoogleGenerativeAI, GenerativeModel } from '@google/generative-ai';
import { IAIClient, IAIClientFactory, IAIMessage, IAIStreamChunk, IAICompletionOptions, AIClientError } from '../interfaces/IAIClient';
import { MessageConverter } from '../utils/messageConverter';
import { logger } from '../../../utils/logger';
import { IAIServiceConfig } from '../../../models/AIServiceConfig';
import { decryptApiKey } from '../../../utils/encryption';

/**
 * Google AI客户端实现
 */
class GoogleAIClient implements IAIClient {
    public readonly provider = 'google';
    private model: GenerativeModel;

    constructor(
        public readonly configId: string,
        public readonly modelName: string,
        private genAI: GoogleGenerativeAI
    ) {
        this.model = genAI.getGenerativeModel({ model: modelName });
    }

    async *createChatCompletionStream(
        messages: IAIMessage[], 
        options?: IAICompletionOptions
    ): AsyncIterable<IAIStreamChunk> {
        try {
            logger.debug(`Google AI 开始流式聊天: ${this.modelName}`);
            
            // 转换消息格式
            const { systemInstruction, contents } = MessageConverter.toGoogleAI(messages);
            
            // 配置生成参数
            const generationConfig = {
                temperature: options?.temperature ?? 0.7,
                maxOutputTokens: options?.maxTokens ?? 2048,
            };

            // 创建聊天会话
            const chatParams: any = {
                generationConfig,
                history: contents.slice(0, -1) // 除了最后一条消息
            };

            // 只有在有系统指令时才添加
            if (systemInstruction) {
                chatParams.systemInstruction = systemInstruction.parts.map(part => part.text).join('\n\n');
            }

            const chat = this.model.startChat(chatParams);

            // 获取最后一条用户消息
            const lastMessage = contents[contents.length - 1];
            if (!lastMessage || lastMessage.role !== 'user') {
                throw new AIClientError('最后一条消息必须是用户消息', this.provider);
            }

            const prompt = lastMessage.parts.map(part => part.text).join('\n');

            // 发送流式请求
            const result = await chat.sendMessageStream(prompt);

            let totalTokens = 0;
            for await (const chunk of result.stream) {
                const text = chunk.text();
                if (text) {
                    totalTokens += text.length; // 简单估算
                    yield {
                        content: text,
                        done: false
                    };
                }
            }

            // 发送完成信号
            yield {
                content: '',
                done: true,
                usage: {
                    totalTokens: totalTokens
                }
            };

            logger.debug(`Google AI 流式聊天完成: ${totalTokens} tokens`);

        } catch (error: any) {
            logger.error('Google AI 流式聊天错误:', error);
            
            yield {
                content: '',
                done: true,
                error: error.message || 'Google AI 服务错误'
            };
            
            throw new AIClientError(
                `Google AI 聊天失败: ${error.message}`,
                this.provider,
                error.code,
                error.status
            );
        }
    }

    async testConnection(): Promise<boolean> {
        try {
            logger.debug('测试 Google AI 连接');
            
            // 发送简单的测试消息
            const result = await this.model.generateContent('Hello');
            const response = await result.response;
            const text = response.text();
            
            logger.debug('Google AI 连接测试成功');
            return !!text;
        } catch (error: any) {
            logger.error('Google AI 连接测试失败:', error);
            return false;
        }
    }

    async getModels(): Promise<string[]> {
        try {
            // Google AI SDK 目前不提供模型列表API
            // 返回常用的模型列表
            return [
                'gemini-1.5-pro',
                'gemini-1.5-flash',
                'gemini-1.0-pro',
                'gemini-pro-vision'
            ];
        } catch (error: any) {
            logger.error('获取 Google AI 模型列表失败:', error);
            return [];
        }
    }

    dispose(): void {
        // Google AI SDK 不需要显式释放资源
        logger.debug('Google AI 客户端已释放');
    }
}

/**
 * Google AI客户端工厂
 */
export class GoogleAIClientFactory implements IAIClientFactory {
    public readonly provider = 'google';

    async createClient(config: IAIServiceConfig, modelName: string): Promise<IAIClient> {
        try {
            logger.info(`创建 Google AI 客户端: ${config.name} - ${modelName}`);
            
            // 解密API密钥
            const apiKey = decryptApiKey(config.apiKey);
            
            // 创建Google AI实例
            const genAI = new GoogleGenerativeAI(apiKey);
            
            // 创建客户端
            const client = new GoogleAIClient(
                config._id.toString(),
                modelName,
                genAI
            );
            
            logger.info(`Google AI 客户端创建成功: ${config.name}`);
            return client;
            
        } catch (error: any) {
            logger.error('创建 Google AI 客户端失败:', error);
            throw new AIClientError(
                `创建 Google AI 客户端失败: ${error.message}`,
                this.provider
            );
        }
    }

    async validateConfig(config: IAIServiceConfig): Promise<boolean> {
        try {
            logger.debug(`验证 Google AI 配置: ${config.name}`);
            
            // 检查必需字段
            if (!config.apiKey) {
                logger.error('Google AI 配置缺少 API 密钥');
                return false;
            }

            // 解密并验证API密钥格式
            const apiKey = decryptApiKey(config.apiKey);
            if (!apiKey.startsWith('AIza')) {
                logger.error('Google AI API 密钥格式无效');
                return false;
            }

            // 创建临时客户端测试连接
            const genAI = new GoogleGenerativeAI(apiKey);
            const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
            
            const result = await model.generateContent('test');
            await result.response;
            
            logger.debug('Google AI 配置验证成功');
            return true;
            
        } catch (error: any) {
            logger.error('Google AI 配置验证失败:', error);
            return false;
        }
    }

    getDefaultConfig(): Partial<IAIServiceConfig> {
        return {
            provider: 'google',
            baseURL: 'https://generativelanguage.googleapis.com',
            maxRetries: 3,
            timeout: 60000,
            rateLimits: {
                requestsPerMinute: 60,
                tokensPerMinute: 100000
            }
        };
    }
}

export default GoogleAIClientFactory;
