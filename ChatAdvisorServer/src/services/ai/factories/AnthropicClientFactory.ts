/**
 * Anthropic客户端工厂
 */

import Anthropic from '@anthropic-ai/sdk';
import { IAIClient, IAIClientFactory, IAIMessage, IAIStreamChunk, IAICompletionOptions, AIClientError } from '../interfaces/IAIClient';
import { MessageConverter } from '../utils/messageConverter';
import { logger } from '../../../utils/logger';
import { IAIServiceConfig } from '../../../models/AIServiceConfig';
import { decryptApiKey } from '../../../utils/encryption';

/**
 * Anthropic客户端实现
 */
class AnthropicClient implements IAIClient {
    public readonly provider = 'anthropic';

    constructor(
        public readonly configId: string,
        public readonly modelName: string,
        private anthropic: Anthropic
    ) {}

    async *createChatCompletionStream(
        messages: IAIMessage[], 
        options?: IAICompletionOptions
    ): AsyncIterable<IAIStreamChunk> {
        try {
            logger.debug(`Anthropic 开始流式聊天: ${this.modelName}`);
            
            // 转换消息格式
            const { system, messages: anthropicMessages } = MessageConverter.toAnthropic(messages);
            
            // 配置请求参数
            const requestParams: Anthropic.MessageCreateParams = {
                model: this.modelName,
                max_tokens: options?.maxTokens ?? 2048,
                temperature: options?.temperature ?? 0.7,
                messages: anthropicMessages,
                stream: true
            };

            // 添加系统消息
            if (system) {
                requestParams.system = system;
            }

            // 发送流式请求
            const stream = await this.anthropic.messages.stream(requestParams);

            let totalTokens = 0;
            for await (const chunk of stream) {
                if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
                    const text = chunk.delta.text;
                    if (text) {
                        totalTokens += text.length; // 简单估算
                        yield {
                            content: text,
                            done: false
                        };
                    }
                } else if (chunk.type === 'message_stop') {
                    // 消息结束
                    yield {
                        content: '',
                        done: true,
                        usage: {
                            totalTokens: totalTokens
                        }
                    };
                    break;
                }
            }

            logger.debug(`Anthropic 流式聊天完成: ${totalTokens} tokens`);

        } catch (error: any) {
            logger.error('Anthropic 流式聊天错误:', error);
            
            yield {
                content: '',
                done: true,
                error: error.message || 'Anthropic 服务错误'
            };
            
            throw new AIClientError(
                `Anthropic 聊天失败: ${error.message}`,
                this.provider,
                error.code,
                error.status
            );
        }
    }

    async testConnection(): Promise<boolean> {
        try {
            logger.debug('测试 Anthropic 连接');
            
            // 发送简单的测试消息
            const response = await this.anthropic.messages.create({
                model: this.modelName,
                max_tokens: 10,
                messages: [{ role: 'user', content: 'Hello' }]
            });
            
            logger.debug('Anthropic 连接测试成功');
            return !!response.content.length;
        } catch (error: any) {
            logger.error('Anthropic 连接测试失败:', error);
            return false;
        }
    }

    async getModels(): Promise<string[]> {
        try {
            // Anthropic SDK 目前不提供模型列表API
            // 返回常用的模型列表
            return [
                'claude-3-5-sonnet-20241022',
                'claude-3-5-haiku-20241022',
                'claude-3-opus-20240229',
                'claude-3-sonnet-20240229',
                'claude-3-haiku-20240307'
            ];
        } catch (error: any) {
            logger.error('获取 Anthropic 模型列表失败:', error);
            return [];
        }
    }

    dispose(): void {
        // Anthropic SDK 不需要显式释放资源
        logger.debug('Anthropic 客户端已释放');
    }
}

/**
 * Anthropic客户端工厂
 */
export class AnthropicClientFactory implements IAIClientFactory {
    public readonly provider = 'anthropic';

    async createClient(config: IAIServiceConfig, modelName: string): Promise<IAIClient> {
        try {
            logger.info(`创建 Anthropic 客户端: ${config.name} - ${modelName}`);
            
            // 解密API密钥
            const apiKey = decryptApiKey(config.apiKey);
            
            // 创建Anthropic实例
            const anthropic = new Anthropic({
                apiKey: apiKey,
                baseURL: config.baseURL || 'https://api.anthropic.com',
                timeout: config.timeout || 60000,
                maxRetries: config.maxRetries || 3
            });
            
            // 创建客户端
            const client = new AnthropicClient(
                config._id.toString(),
                modelName,
                anthropic
            );
            
            logger.info(`Anthropic 客户端创建成功: ${config.name}`);
            return client;
            
        } catch (error: any) {
            logger.error('创建 Anthropic 客户端失败:', error);
            throw new AIClientError(
                `创建 Anthropic 客户端失败: ${error.message}`,
                this.provider
            );
        }
    }

    async validateConfig(config: IAIServiceConfig): Promise<boolean> {
        try {
            logger.debug(`验证 Anthropic 配置: ${config.name}`);
            
            // 检查必需字段
            if (!config.apiKey) {
                logger.error('Anthropic 配置缺少 API 密钥');
                return false;
            }

            // 解密并验证API密钥格式
            const apiKey = decryptApiKey(config.apiKey);
            if (!apiKey.startsWith('sk-ant-')) {
                logger.error('Anthropic API 密钥格式无效');
                return false;
            }

            // 创建临时客户端测试连接
            const anthropic = new Anthropic({
                apiKey: apiKey,
                baseURL: config.baseURL || 'https://api.anthropic.com'
            });
            
            const response = await anthropic.messages.create({
                model: 'claude-3-haiku-20240307',
                max_tokens: 10,
                messages: [{ role: 'user', content: 'test' }]
            });
            
            logger.debug('Anthropic 配置验证成功');
            return !!response.content.length;
            
        } catch (error: any) {
            logger.error('Anthropic 配置验证失败:', error);
            return false;
        }
    }

    getDefaultConfig(): Partial<IAIServiceConfig> {
        return {
            provider: 'anthropic',
            baseURL: 'https://api.anthropic.com',
            maxRetries: 3,
            timeout: 60000,
            rateLimits: {
                requestsPerMinute: 50,
                tokensPerMinute: 80000
            }
        };
    }
}

export default AnthropicClientFactory;
