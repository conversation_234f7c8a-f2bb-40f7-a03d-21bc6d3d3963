/**
 * 统一的AI客户端接口
 * 用于抽象不同AI服务提供商的差异
 */

export interface IAIMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

export interface IAIStreamChunk {
    content?: string;
    done?: boolean;
    error?: string;
    usage?: {
        promptTokens?: number;
        completionTokens?: number;
        totalTokens?: number;
    };
}

export interface IAICompletionOptions {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
    [key: string]: any; // 允许提供商特定的选项
}

export interface IAIClient {
    readonly provider: string;
    readonly configId: string;
    readonly modelName: string;
    
    /**
     * 创建聊天完成（流式）
     */
    createChatCompletionStream(
        messages: IAIMessage[], 
        options?: IAICompletionOptions
    ): AsyncIterable<IAIStreamChunk>;
    
    /**
     * 测试连接
     */
    testConnection(): Promise<boolean>;
    
    /**
     * 获取可用模型列表
     */
    getModels(): Promise<string[]>;
    
    /**
     * 释放资源
     */
    dispose?(): void;
}

export interface IAIClientFactory {
    readonly provider: string;
    
    /**
     * 创建客户端实例
     */
    createClient(config: any, modelName: string): Promise<IAIClient>;
    
    /**
     * 验证配置
     */
    validateConfig(config: any): Promise<boolean>;
    
    /**
     * 获取默认配置模板
     */
    getDefaultConfig(): any;
}

/**
 * AI客户端错误类型
 */
export class AIClientError extends Error {
    constructor(
        message: string,
        public readonly provider: string,
        public readonly code?: string,
        public readonly statusCode?: number
    ) {
        super(message);
        this.name = 'AIClientError';
    }
}

/**
 * 提供商特定的错误类型
 */
export class ProviderError extends AIClientError {
    constructor(
        message: string,
        provider: string,
        public readonly originalError?: any
    ) {
        super(message, provider);
        this.name = 'ProviderError';
    }
}
