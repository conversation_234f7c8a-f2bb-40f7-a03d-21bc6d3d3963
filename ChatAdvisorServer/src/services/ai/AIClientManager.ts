/**
 * 统一的AI客户端管理器
 * 负责管理不同AI服务提供商的客户端实例
 */

import { IAIClient, IAIClientFactory, AIClientError } from './interfaces/IAIClient';
import { OpenAIClientFactory } from './factories/OpenAIClientFactory';
import { GoogleAIClientFactory } from './factories/GoogleAIClientFactory';
import { AnthropicClientFactory } from './factories/AnthropicClientFactory';
import { logger } from '../../utils/logger';
import { IAIServiceConfig } from '../../models/AIServiceConfig';
import AIConfigCache from '../AIConfigCache';
// Confirmed via mcp-feedback-enhanced: 将流程中的动态引入统一提升至头部静态引入
import ActiveAIConfigManager from '../ActiveAIConfigManager';
import AIServiceConfig from '../../models/AIServiceConfig';
import AIServiceModel from '../../models/AIServiceModel';

interface ClientCacheItem {
    client: IAIClient;
    configId: string;
    modelName: string;
    timestamp: number;
}

/**
 * AI客户端管理器
 */
export class AIClientManager {
    private clientCache = new Map<string, ClientCacheItem>();
    private factories = new Map<string, IAIClientFactory>();
    private readonly CACHE_TTL = 30 * 60 * 1000; // 30分钟
    private readonly CLEANUP_INTERVAL = 10 * 60 * 1000; // 10分钟清理一次

    constructor() {
        // 注册客户端工厂
        this.registerFactory(new OpenAIClientFactory());
        this.registerFactory(new GoogleAIClientFactory());
        this.registerFactory(new AnthropicClientFactory());

        // 定期清理过期客户端
        setInterval(() => {
            this.cleanupExpiredClients();
        }, this.CLEANUP_INTERVAL);

        logger.info('AI客户端管理器初始化完成');
    }

    /**
     * 注册客户端工厂
     */
    private registerFactory(factory: IAIClientFactory): void {
        this.factories.set(factory.provider, factory);
        logger.debug(`注册AI客户端工厂: ${factory.provider}`);
    }

    /**
     * 获取客户端实例（带缓存）
     */
    async getClient(configId: string, modelName: string): Promise<IAIClient> {
        const cacheKey = `${configId}:${modelName}`;
        
        // 检查缓存
        const cached = this.clientCache.get(cacheKey);
        if (cached && !this.isExpired(cached)) {
            logger.debug(`从缓存获取AI客户端: ${cached.client.provider} - ${modelName}`);
            return cached.client;
        }

        // 从缓存获取配置
        let config = await AIConfigCache.getCachedConfig(configId);
        if (!config) {
            // 如果缓存中没有，从数据库加载（头部静态引入）
            config = await AIServiceConfig.findById(configId).select('+apiKey');
            if (!config) {
                throw new AIClientError(`AI服务配置不存在: ${configId}`, 'unknown');
            }
            if (!config.isActive) {
                throw new AIClientError(`AI服务配置已禁用: ${config.name}`, config.provider);
            }
            // 缓存配置
            await AIConfigCache.setCachedConfig(config);
        }

        // 获取对应的工厂
        const factory = this.factories.get(config.provider);
        if (!factory) {
            throw new AIClientError(
                `不支持的AI服务提供商: ${config.provider}`,
                config.provider
            );
        }

        // 创建客户端
        const client = await factory.createClient(config, modelName);

        // 缓存客户端
        this.clientCache.set(cacheKey, {
            client,
            configId,
            modelName,
            timestamp: Date.now()
        });

        logger.info(`创建并缓存AI客户端: ${client.provider} - ${modelName}`);
        return client;
    }

    /**
     * 获取默认客户端和模型信息（优先使用启用配置）
     */
    async getDefaultClientWithModel(): Promise<{
        client: IAIClient;
        modelName: string;
        configName: string;
        isActiveConfig: boolean;
    }> {
        // 首先尝试获取启用配置
        try {
            const activeConfig = await ActiveAIConfigManager.getActiveConfig();
            if (activeConfig && activeConfig.configId && activeConfig.modelId) {
                const configName = (activeConfig.configId as any).name;
                const modelName = (activeConfig.modelId as any).modelName;
                const modelDisplayName = (activeConfig.modelId as any).displayName;

                logger.info(`使用启用配置: ${configName} - ${modelDisplayName}`);

                const client = await this.getClient(
                    (activeConfig.configId as any)._id.toString(),
                    modelName
                );

                return {
                    client,
                    modelName: modelDisplayName,
                    configName,
                    isActiveConfig: true
                };
            }
        } catch (error) {
            logger.warn('获取启用配置失败，尝试使用默认配置:', error);
        }

        // 回退到默认配置
        const defaultConfig = await AIServiceConfig.findOne({
            isDefault: true,
            isActive: true
        }).select('+apiKey');

        if (!defaultConfig) {
            throw new AIClientError('未找到可用的AI服务配置', 'unknown');
        }

        // 获取默认模型
        const defaultModel = await AIServiceModel.findOne({
            configId: defaultConfig._id,
            isActive: true
        }).sort({ isDefault: -1, createdAt: -1 });

        if (!defaultModel) {
            throw new AIClientError(`配置 ${defaultConfig.name} 没有可用的模型`, defaultConfig.provider);
        }

        logger.info(`使用默认配置: ${defaultConfig.name} - ${defaultModel.displayName}`);

        const client = await this.getClient(
            defaultConfig._id.toString(),
            defaultModel.modelName
        );

        return {
            client,
            modelName: defaultModel.displayName,
            configName: defaultConfig.name,
            isActiveConfig: false
        };
    }

    /**
     * 验证配置
     */
    async validateConfig(config: IAIServiceConfig): Promise<boolean> {
        const factory = this.factories.get(config.provider);
        if (!factory) {
            logger.error(`不支持的AI服务提供商: ${config.provider}`);
            return false;
        }

        return await factory.validateConfig(config);
    }

    /**
     * 获取提供商的默认配置
     */
    getDefaultConfig(provider: string): any {
        const factory = this.factories.get(provider);
        if (!factory) {
            throw new AIClientError(`不支持的AI服务提供商: ${provider}`, provider);
        }

        return factory.getDefaultConfig();
    }

    /**
     * 获取支持的提供商列表
     */
    getSupportedProviders(): string[] {
        return Array.from(this.factories.keys());
    }

    /**
     * 清除客户端缓存
     */
    clearClientCache(configId?: string): void {
        if (configId) {
            // 清除特定配置的缓存
            const keysToDelete: string[] = [];
            for (const [key, item] of this.clientCache.entries()) {
                if (item.configId === configId) {
                    keysToDelete.push(key);
                    item.client.dispose?.();
                }
            }
            keysToDelete.forEach(key => this.clientCache.delete(key));
            logger.debug(`清除配置 ${configId} 的客户端缓存`);
        } else {
            // 清除所有缓存
            for (const item of this.clientCache.values()) {
                item.client.dispose?.();
            }
            this.clientCache.clear();
            logger.debug('清除所有客户端缓存');
        }
    }

    /**
     * 检查缓存项是否过期
     */
    private isExpired(item: ClientCacheItem): boolean {
        return Date.now() - item.timestamp > this.CACHE_TTL;
    }

    /**
     * 清理过期的客户端
     */
    private cleanupExpiredClients(): void {
        const now = Date.now();
        const expiredKeys: string[] = [];

        for (const [key, item] of this.clientCache.entries()) {
            if (now - item.timestamp > this.CACHE_TTL) {
                expiredKeys.push(key);
                item.client.dispose?.();
            }
        }

        expiredKeys.forEach(key => this.clientCache.delete(key));

        if (expiredKeys.length > 0) {
            logger.debug(`清理了 ${expiredKeys.length} 个过期的AI客户端`);
        }
    }
}

// 创建单例实例
export const aiClientManager = new AIClientManager();
export default aiClientManager;
