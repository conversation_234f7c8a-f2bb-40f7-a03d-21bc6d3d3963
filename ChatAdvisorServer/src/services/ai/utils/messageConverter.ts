/**
 * 消息格式转换工具
 * 用于在不同AI服务提供商之间转换消息格式
 */

import { IAIMessage } from '../interfaces/IAIClient';

/**
 * OpenAI消息格式
 */
export interface OpenAIMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

/**
 * Google AI消息格式
 */
export interface GoogleAIContent {
    role: 'user' | 'model';
    parts: Array<{ text: string }>;
}

/**
 * Anthropic消息格式
 */
export interface AnthropicMessage {
    role: 'user' | 'assistant';
    content: string;
}

/**
 * 消息转换器类
 */
export class MessageConverter {
    /**
     * 转换为OpenAI格式
     */
    static toOpenAI(messages: IAIMessage[]): OpenAIMessage[] {
        return messages.map(msg => ({
            role: msg.role,
            content: msg.content
        }));
    }

    /**
     * 转换为Google AI格式
     */
    static toGoogleAI(messages: IAIMessage[]): {
        systemInstruction?: { parts: Array<{ text: string }> };
        contents: GoogleAIContent[];
    } {
        const systemMessages = messages.filter(msg => msg.role === 'system');
        const conversationMessages = messages.filter(msg => msg.role !== 'system');

        // 合并所有系统消息
        const systemInstruction = systemMessages.length > 0 ? {
            parts: [{ text: systemMessages.map(msg => msg.content).join('\n\n') }]
        } : undefined;

        // 转换对话消息
        const contents: GoogleAIContent[] = conversationMessages.map(msg => ({
            role: msg.role === 'assistant' ? 'model' : 'user',
            parts: [{ text: msg.content }]
        }));

        return { systemInstruction, contents };
    }

    /**
     * 转换为Anthropic格式
     */
    static toAnthropic(messages: IAIMessage[]): {
        system?: string;
        messages: AnthropicMessage[];
    } {
        const systemMessages = messages.filter(msg => msg.role === 'system');
        const conversationMessages = messages.filter(msg => msg.role !== 'system');

        // 合并所有系统消息
        const system = systemMessages.length > 0 
            ? systemMessages.map(msg => msg.content).join('\n\n')
            : undefined;

        // 转换对话消息
        const anthropicMessages: AnthropicMessage[] = conversationMessages.map(msg => ({
            role: msg.role === 'assistant' ? 'assistant' : 'user',
            content: msg.content
        }));

        return { system, messages: anthropicMessages };
    }

    /**
     * 从OpenAI格式转换
     */
    static fromOpenAI(messages: OpenAIMessage[]): IAIMessage[] {
        return messages.map(msg => ({
            role: msg.role,
            content: msg.content
        }));
    }

    /**
     * 从Google AI格式转换
     */
    static fromGoogleAI(
        contents: GoogleAIContent[], 
        systemInstruction?: { parts: Array<{ text: string }> }
    ): IAIMessage[] {
        const messages: IAIMessage[] = [];

        // 添加系统消息
        if (systemInstruction?.parts) {
            const systemContent = systemInstruction.parts
                .map(part => part.text)
                .join('\n\n');
            if (systemContent.trim()) {
                messages.push({
                    role: 'system',
                    content: systemContent
                });
            }
        }

        // 添加对话消息
        contents.forEach(content => {
            const text = content.parts.map(part => part.text).join('\n\n');
            if (text.trim()) {
                messages.push({
                    role: content.role === 'model' ? 'assistant' : 'user',
                    content: text
                });
            }
        });

        return messages;
    }

    /**
     * 从Anthropic格式转换
     */
    static fromAnthropic(
        messages: AnthropicMessage[], 
        system?: string
    ): IAIMessage[] {
        const result: IAIMessage[] = [];

        // 添加系统消息
        if (system?.trim()) {
            result.push({
                role: 'system',
                content: system
            });
        }

        // 添加对话消息
        messages.forEach(msg => {
            result.push({
                role: msg.role,
                content: msg.content
            });
        });

        return result;
    }

    /**
     * 验证消息格式
     */
    static validateMessages(messages: IAIMessage[]): boolean {
        if (!Array.isArray(messages)) {
            return false;
        }

        return messages.every(msg => 
            msg && 
            typeof msg.role === 'string' && 
            ['system', 'user', 'assistant'].includes(msg.role) &&
            typeof msg.content === 'string'
        );
    }

    /**
     * 清理和标准化消息
     */
    static normalizeMessages(messages: IAIMessage[]): IAIMessage[] {
        return messages
            .filter(msg => msg.content?.trim()) // 过滤空消息
            .map(msg => ({
                role: msg.role,
                content: msg.content.trim()
            }));
    }
}
