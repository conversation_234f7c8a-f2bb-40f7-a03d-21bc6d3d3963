import { Router } from 'express';
import { body, param, query } from 'express-validator';
import AIConfigController from '../controllers/AIConfigController';
import { adminAuth } from '../middleware/adminAuth';

const router = Router();

// 应用管理员认证中间件
router.use(adminAuth);

// 验证规则
const createConfigValidation = [
    body('name')
        .notEmpty()
        .withMessage('配置名称不能为空')
        .isLength({ max: 100 })
        .withMessage('配置名称不能超过100个字符'),
    body('baseURL')
        .notEmpty()
        .withMessage('API端点不能为空')
        .isURL({ protocols: ['http', 'https'] })
        .withMessage('API端点必须是有效的HTTP/HTTPS URL'),
    body('apiKey')
        .notEmpty()
        .withMessage('API密钥不能为空')
        .isLength({ min: 10 })
        .withMessage('API密钥长度至少10个字符'),
    body('provider')
        .optional()
        .isIn(['openai', 'anthropic', 'google', 'azure', 'custom'])
        .withMessage('无效的服务提供商'),
    body('description')
        .optional()
        .isLength({ max: 500 })
        .withMessage('描述不能超过500个字符'),
    body('maxRetries')
        .optional()
        .isInt({ min: 0, max: 10 })
        .withMessage('最大重试次数必须在0-10之间'),
    body('timeout')
        .optional()
        .isInt({ min: 5000, max: 300000 })
        .withMessage('超时时间必须在5000-300000毫秒之间'),
    body('proxyConfig.enabled')
        .optional()
        .isBoolean()
        .withMessage('代理启用状态必须是布尔值'),
    body('proxyConfig.url')
        .optional()
        .isURL()
        .withMessage('代理URL必须是有效的URL'),
    body('rateLimits.requestsPerMinute')
        .optional()
        .isInt({ min: 1, max: 1000 })
        .withMessage('每分钟请求数必须在1-1000之间'),
    body('rateLimits.tokensPerMinute')
        .optional()
        .isInt({ min: 1000, max: 1000000 })
        .withMessage('每分钟token数必须在1000-1000000之间')
];

const updateConfigValidation = [
    param('id')
        .isMongoId()
        .withMessage('无效的配置ID'),
    body('name')
        .optional()
        .notEmpty()
        .withMessage('配置名称不能为空')
        .isLength({ max: 100 })
        .withMessage('配置名称不能超过100个字符'),
    body('baseURL')
        .optional()
        .isURL({ protocols: ['http', 'https'] })
        .withMessage('API端点必须是有效的HTTP/HTTPS URL'),
    body('apiKey')
        .optional()
        .isLength({ min: 10 })
        .withMessage('API密钥长度至少10个字符'),
    body('provider')
        .optional()
        .isIn(['openai', 'anthropic', 'google', 'azure', 'custom'])
        .withMessage('无效的服务提供商'),
    body('description')
        .optional()
        .isLength({ max: 500 })
        .withMessage('描述不能超过500个字符'),
    body('isActive')
        .optional()
        .isBoolean()
        .withMessage('启用状态必须是布尔值'),
    body('maxRetries')
        .optional()
        .isInt({ min: 0, max: 10 })
        .withMessage('最大重试次数必须在0-10之间'),
    body('timeout')
        .optional()
        .isInt({ min: 5000, max: 300000 })
        .withMessage('超时时间必须在5000-300000毫秒之间')
];

const idValidation = [
    param('id')
        .isMongoId()
        .withMessage('无效的配置ID')
];

const listValidation = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('页码必须是大于0的整数'),
    query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('每页数量必须在1-100之间'),
    query('provider')
        .optional()
        .isIn(['openai', 'anthropic', 'google', 'azure', 'custom'])
        .withMessage('无效的服务提供商'),
    query('isActive')
        .optional()
        .isIn(['true', 'false'])
        .withMessage('启用状态必须是true或false')
];

const updateModelValidation = [
    param('id')
        .isMongoId()
        .withMessage('无效的模型ID'),
    body('displayName')
        .optional()
        .notEmpty()
        .withMessage('显示名称不能为空')
        .isLength({ max: 200 })
        .withMessage('显示名称不能超过200个字符'),
    body('description')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('描述不能超过1000个字符'),
    body('maxTokens')
        .optional()
        .isInt({ min: 1, max: 1000000 })
        .withMessage('最大token数必须在1-1000000之间'),
    body('pricing.inputPrice')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('输入价格必须大于等于0'),
    body('pricing.outputPrice')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('输出价格必须大于等于0'),
    body('isActive')
        .optional()
        .isBoolean()
        .withMessage('启用状态必须是布尔值'),
    body('sortOrder')
        .optional()
        .isInt({ min: 0 })
        .withMessage('排序必须是大于等于0的整数')
];

// AI配置管理路由
router.get('/configs', listValidation, AIConfigController.getConfigs);
router.get('/configs/:id', idValidation, AIConfigController.getConfig);
router.post('/configs', createConfigValidation, AIConfigController.createConfig);
router.put('/configs/:id', updateConfigValidation, AIConfigController.updateConfig);
router.delete('/configs/:id', idValidation, AIConfigController.deleteConfig);

// 配置操作路由
router.post('/configs/:id/test', idValidation, AIConfigController.testConfig);
router.post('/configs/:id/set-default', idValidation, AIConfigController.setDefaultConfig);
router.post('/configs/:configId/sync-models', 
    param('configId').isMongoId().withMessage('无效的配置ID'), 
    AIConfigController.syncModels
);

// 模型管理路由
router.get('/models', AIConfigController.getModels);
router.put('/models/:id', updateModelValidation, AIConfigController.updateModel);

// 手动添加模型路由
router.post('/configs/:configId/models/manual',
    param('configId').isMongoId().withMessage('无效的配置ID'),
    body('models').isArray({ min: 1 }).withMessage('请提供有效的模型列表'),
    body('models.*.modelName').notEmpty().withMessage('模型名称不能为空'),
    AIConfigController.addManualModels
);
router.delete('/configs/:configId/models/:modelId/manual',
    param('configId').isMongoId().withMessage('无效的配置ID'),
    param('modelId').isMongoId().withMessage('无效的模型ID'),
    AIConfigController.deleteManualModel
);

// 统计路由
router.get('/configs/:configId/usage-stats',
    param('configId').isMongoId().withMessage('无效的配置ID'),
    query('startDate').optional().isISO8601().withMessage('开始日期格式无效'),
    query('endDate').optional().isISO8601().withMessage('结束日期格式无效'),
    AIConfigController.getUsageStats
);

// 启用配置路由
router.post('/configs/:id/activate',
    param('id').isMongoId().withMessage('无效的配置ID'),
    body('modelId').isMongoId().withMessage('无效的模型ID'),
    AIConfigController.activateConfig
);
router.get('/active-config', AIConfigController.getActiveConfig);
router.delete('/active-config', AIConfigController.deactivateConfig);
router.get('/configs/:id/models',
    param('id').isMongoId().withMessage('无效的配置ID'),
    AIConfigController.getConfigModels
);
router.get('/active-config/stats', AIConfigController.getActiveConfigStats);

export default router;
