#!/bin/bash

# 测试不同的 Gemini 模型名称格式

API_TOKEN="sk-aOxnzCfQjl69VGHV18578aFf7fC74cEcB896EcE8Ed8553E2"
BASE_URL="http://localhost:3000/v1"

echo "=== 测试不同的 Gemini 2.5 Flash 模型名称格式 ==="

# 测试格式1: gemini-2.5-flash
echo "1. 测试模型名称: gemini-2.5-flash"
curl -X POST "${BASE_URL}/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [{"role": "user", "content": "Hello, test 1"}],
    "stream": true,
    "max_tokens": 100
  }' \
  --no-buffer \
  -s | head -5

echo -e "\n--- 分隔线 ---\n"

# 测试格式2: gemini-2.5-flash-latest
echo "2. 测试模型名称: gemini-2.5-flash-latest"
curl -X POST "${BASE_URL}/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -d '{
    "model": "gemini-2.5-flash-latest",
    "messages": [{"role": "user", "content": "Hello, test 2"}],
    "stream": true,
    "max_tokens": 100
  }' \
  --no-buffer \
  -s | head -5

echo -e "\n--- 分隔线 ---\n"

# 测试格式3: models/gemini-2.5-flash
echo "3. 测试模型名称: models/gemini-2.5-flash"
curl -X POST "${BASE_URL}/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -d '{
    "model": "models/gemini-2.5-flash",
    "messages": [{"role": "user", "content": "Hello, test 3"}],
    "stream": true,
    "max_tokens": 100
  }' \
  --no-buffer \
  -s | head -5

echo -e "\n--- 分隔线 ---\n"

# 测试格式4: gemini-1.5-flash (对比测试)
echo "4. 测试模型名称: gemini-1.5-flash (对比)"
curl -X POST "${BASE_URL}/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -d '{
    "model": "gemini-1.5-flash",
    "messages": [{"role": "user", "content": "Hello, test 4"}],
    "stream": true,
    "max_tokens": 100
  }' \
  --no-buffer \
  -s | head -5

echo -e "\n=== 测试完成 ==="
