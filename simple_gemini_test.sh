#!/bin/bash

# 简单的 Gemini 2.5 Flash 测试命令 (OpenAI 兼容格式)

echo "测试 Gemini 2.5 Flash 聊天功能 (OpenAI 兼容接口)..."

curl -X POST "http://localhost:3000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-aOxnzCfQjl69VGHV18578aFf7fC74cEcB896EcE8Ed8553E2" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {
        "role": "user",
        "content": "你好，这是一个 Gemini 2.5 Flash 模型的测试。请回复确认你收到了这条消息。"
      }
    ],
    "stream": true,
    "temperature": 0.9,
    "max_tokens": 2048
  }' \
  --no-buffer \
  -v
