#!/bin/bash

# 测试同步模型API的脚本

# 配置参数
ADMIN_BASE_URL="http://localhost:3001"
CONFIG_ID="YOUR_CONFIG_ID_HERE"  # 需要替换为实际的配置ID
ADMIN_TOKEN="YOUR_ADMIN_TOKEN_HERE"  # 需要替换为实际的管理员token

echo "=== 测试同步模型API ==="
echo "Admin Base URL: ${ADMIN_BASE_URL}"
echo "Config ID: ${CONFIG_ID}"
echo ""

# 1. 测试同步模型接口
echo "1. 测试同步模型接口..."
curl -X POST "${ADMIN_BASE_URL}/admin/ai/configs/${CONFIG_ID}/sync-models" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -d '{}' \
  -v

echo -e "\n\n=== 分隔线 ===\n"

# 2. 获取同步后的模型列表
echo "2. 获取同步后的模型列表..."
curl -X GET "${ADMIN_BASE_URL}/admin/ai/models?configId=${CONFIG_ID}" \
  -H "Authorization: Bearer ${ADMIN_TOKEN}" \
  -H "Content-Type: application/json" \
  -v

echo -e "\n\n=== 测试完成 ==="

# 使用说明
echo ""
echo "使用说明："
echo "1. 请将 CONFIG_ID 替换为实际的AI配置ID"
echo "2. 请将 ADMIN_TOKEN 替换为实际的管理员认证token"
echo "3. 确保AI配置中已正确设置API密钥"
echo "4. 确保目标服务（如OneAPI）正在运行并可访问"
