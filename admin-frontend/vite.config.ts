import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// 读取package.json获取版本号
const packageJson = require('./package.json')

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 根据环境确定端口和API目标
  const isProduction = mode === 'production'
  const port = parseInt(process.env.PORT || '3000')

  console.log(`[Vite Config] Command: ${command}, Mode: ${mode}, Production: ${isProduction}`)

  // 根据环境自动确定后端API端点（开发33001；预览/线上后端53011）
  let apiTarget = 'http://localhost:33001'
  const onlineBackend = 'http://localhost:53011'
  if (isProduction) {
    apiTarget = onlineBackend
  }

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    define: {
      // 将版本号注入到环境变量中
      'process.env.REACT_APP_VERSION': JSON.stringify(packageJson.version),
    },
    server: {
      port,
      host: true, // 允许外部访问
      cors: true, // 开启CORS，便于本地不同端口联调
      proxy: {
        // 针对本地模型接口提供专用代理，避免跨域
        '/api/models': {
          target: apiTarget,
          changeOrigin: true,
        },
        '/api': {
          target: apiTarget,
          changeOrigin: true,
        },
      },
      // 确保SPA路由正常工作
      historyApiFallback: true,
    },
    preview: {
      port,
      host: true, // 允许外部访问
      // 允许特定主机访问
      allowedHosts: ['*'],
      proxy: {
        // 与开发一致，为本地模型接口提供专用代理
        '/api/models': {
          target: apiTarget,
          changeOrigin: true,
        },
        '/api': {
          target: onlineBackend,
          changeOrigin: true,
        },
      },
    },
    build: {
      outDir: 'dist',
      sourcemap: true,
    },
  }
})
