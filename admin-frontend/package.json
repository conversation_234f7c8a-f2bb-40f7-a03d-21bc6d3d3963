{"name": "chatadvisor-admin", "private": true, "version": "2.0.1", "type": "module", "scripts": {"dev": "vite", "dev:34001": "PORT=34001 vite", "dev:54001": "PORT=54001 vite", "build": "vite build", "build:prod": "vite build --mode production", "preview": "vite preview", "preview:34001": "PORT=34001 vite preview", "preview:54001": "PORT=54001 vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "pm-release": "yarn build:prod && pm2 start pm2.config.cjs --only admin-frontend", "pm-start-only": "pm2 start pm2.config.cjs --only admin-frontend", "pm-test": "node scripts/test-pm2-config.cjs", "pm-stop": "pm2 stop admin-frontend", "pm-restart": "pm2 restart admin-frontend", "pm-logs": "pm2 logs admin-frontend"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@types/qrcode": "^1.5.5", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "serve": "^14.2.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}}