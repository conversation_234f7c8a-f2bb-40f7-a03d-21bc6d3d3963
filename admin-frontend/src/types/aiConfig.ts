export interface AIServiceConfig {
  _id: string;
  name: string;
  description?: string;
  baseURL: string;
  apiKey: string;
  provider: 'openai' | 'anthropic' | 'google' | 'azure' | 'custom';
  isActive: boolean;
  isDefault: boolean;
  maxRetries: number;
  timeout: number;
  proxyConfig?: {
    enabled: boolean;
    url?: string;
  };
  rateLimits?: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
  createdAt: string;
  updatedAt: string;
  createdBy: {
    _id: string;
    username: string;
    email: string;
  };
  lastUsedAt?: string;
}

export interface AIServiceModel {
  _id: string;
  configId: string;
  modelName: string;
  displayName: string;
  description?: string;
  maxTokens: number;
  supportedFeatures: string[];
  pricing: {
    inputPrice: number;
    outputPrice: number;
    currency: string;
  };
  parameters: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
  };
  // 存储模型的完整原始信息
  modelInfo?: any;
  // 标记价格是否被手动修改过
  isPricingCustomized: boolean;
  // 模型来源：sync(同步获取) | manual(手动添加)
  source: 'sync' | 'manual';
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export interface AIConfigUsageStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  totalTokens: number;
  totalCost: number;
  averageResponseTime: number;
}

export interface TestResult {
  success: boolean;
  models?: string[];
  error?: string;
  responseTime?: number;
}

export interface CreateConfigRequest {
  name: string;
  description?: string;
  baseURL: string;
  apiKey: string;
  provider?: 'openai' | 'anthropic' | 'google' | 'azure' | 'custom';
  maxRetries?: number;
  timeout?: number;
  proxyConfig?: {
    enabled: boolean;
    url?: string;
  };
  rateLimits?: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
}

export interface UpdateConfigRequest extends Partial<CreateConfigRequest> {
  isActive?: boolean;
}

export interface ConfigListResponse {
  success: boolean;
  data: {
    configs: AIServiceConfig[];
    total: number;
    page: number;
    limit: number;
  };
}

export interface ConfigResponse {
  success: boolean;
  data: AIServiceConfig;
  message?: string;
}

export interface ModelListResponse {
  success: boolean;
  data: {
    models: AIServiceModel[];
    total: number;
    page: number;
    limit: number;
  };
}

export interface TestResponse {
  success: boolean;
  data: TestResult;
}

export interface UsageStatsResponse {
  success: boolean;
  data: AIConfigUsageStats;
}

export interface ActiveAIConfig {
  _id: string;
  configId: {
    _id: string;
    name: string;
    provider: string;
    isActive: boolean;
  };
  modelId: {
    _id: string;
    modelName: string;
    displayName: string;
    maxTokens: number;
    pricing: {
      inputPrice: number;
      outputPrice: number;
      currency: string;
    };
  };
  activatedAt: string;
  activatedBy: {
    _id: string;
    username: string;
    email: string;
  };
}

export interface ActiveConfigStats {
  hasActiveConfig: boolean;
  configId?: string;
  configName?: string;
  modelId?: string;
  modelName?: string;
  activatedAt?: string;
  activatedBy?: string;
}

export interface ActivateConfigRequest {
  modelId: string;
}

export interface ActiveConfigResponse {
  success: boolean;
  data: ActiveAIConfig | null;
}

export interface ActiveConfigStatsResponse {
  success: boolean;
  data: ActiveConfigStats;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: any[];
}

// 本地模型API响应类型
export interface LocalModelsApiResponse {
  data: {
    [configId: string]: string[];
  };
  message: string;
  success: boolean;
}

// 手动添加模型相关类型
export interface ManualModelRequest {
  modelName: string;
  displayName?: string;
  description?: string;
  maxTokens?: number;
  supportedFeatures?: string[];
  pricing?: {
    inputPrice?: number;
    outputPrice?: number;
    currency?: string;
  };
  parameters?: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
  };
}

export interface BatchManualModelRequest {
  models: ManualModelRequest[];
}

export interface ManualModelResponse extends ApiResponse {
  data: AIServiceModel[];
}
