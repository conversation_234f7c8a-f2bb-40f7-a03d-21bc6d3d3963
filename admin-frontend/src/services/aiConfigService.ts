import { request } from './api';
import {
  AIServiceConfig,
  AIServiceModel,
  CreateConfigRequest,
  UpdateConfigRequest,
  ConfigListResponse,
  ConfigResponse,
  ModelListResponse,
  TestResponse,
  UsageStatsResponse,
  ActiveAIConfig,
  ActiveConfigStats,
  ActivateConfigRequest,
  ActiveConfigResponse,
  ActiveConfigStatsResponse,
  ApiResponse,
  ManualModelRequest,
  BatchManualModelRequest,
  ManualModelResponse
} from '../types/aiConfig';

const API_BASE = '/admin/ai';

// （移除本地地址特殊处理，统一走后端同步逻辑）

// 获取AI配置列表
export const getConfigs = async (params?: {
  page?: number;
  limit?: number;
  search?: string;
  provider?: string;
  isActive?: boolean;
}): Promise<ConfigListResponse> => {
  const response = await request.get(`${API_BASE}/configs`, { params });
  if (!response.success || !response.data) {
    throw new Error(response.message || '获取配置列表失败');
  }
  return response as ConfigListResponse;
};

// 获取单个AI配置
export const getConfig = async (id: string): Promise<ConfigResponse> => {
  const response = await request.get(`${API_BASE}/configs/${id}`);
  if (!response.success || !response.data) {
    throw new Error(response.message || '获取配置失败');
  }
  return response as ConfigResponse;
};

// 创建AI配置
export const createConfig = async (data: CreateConfigRequest): Promise<ConfigResponse> => {
  const response = await request.post(`${API_BASE}/configs`, data);
  if (!response.success || !response.data) {
    throw new Error(response.message || '创建配置失败');
  }
  return response as ConfigResponse;
};

// 更新AI配置
export const updateConfig = async (id: string, data: UpdateConfigRequest): Promise<ConfigResponse> => {
  const response = await request.put(`${API_BASE}/configs/${id}`, data);
  if (!response.success || !response.data) {
    throw new Error(response.message || '更新配置失败');
  }
  return response as ConfigResponse;
};

// 删除AI配置
export const deleteConfig = async (id: string): Promise<ApiResponse> => {
  return await request.delete(`${API_BASE}/configs/${id}`);
};

// 测试AI配置连接
export const testConfig = async (id: string): Promise<TestResponse> => {
  const response = await request.post(`${API_BASE}/configs/${id}/test`);
  if (!response.success || !response.data) {
    throw new Error(response.message || '测试配置失败');
  }
  return response as TestResponse;
};

// 设置默认配置
export const setDefaultConfig = async (id: string): Promise<ApiResponse> => {
  return await request.post(`${API_BASE}/configs/${id}/set-default`);
};

// （移除 fetchLocalModels，本地与远程统一走后端同步）

// 同步模型列表（统一处理：后端基于配置调用 OpenAI 兼容 /v1/models）
export const syncModels = async (
  configId: string
): Promise<ApiResponse<AIServiceModel[]>> => {
  try {
    return await request.post(
      `${API_BASE}/configs/${configId}/sync-models`,
      undefined,
      { timeout: 120000 }
    );
  } catch (error) {
    console.error('同步模型失败:', error);
    throw error;
  }
};

// 获取模型列表
export const getModels = async (params?: {
  configId?: string;
  page?: number;
  limit?: number;
  search?: string;
}): Promise<ModelListResponse> => {
  const response = await request.get(`${API_BASE}/models`, { params });
  if (!response.success || !response.data) {
    throw new Error(response.message || '获取模型列表失败');
  }
  return response as ModelListResponse;
};

// 更新模型配置
export const updateModel = async (id: string, data: Partial<AIServiceModel>): Promise<ApiResponse<AIServiceModel>> => {
  return await request.put(`${API_BASE}/models/${id}`, data);
};

// 获取使用统计
export const getUsageStats = async (
  configId: string,
  params?: {
    startDate?: string;
    endDate?: string;
  }
): Promise<UsageStatsResponse> => {
  const response = await request.get(`${API_BASE}/configs/${configId}/usage-stats`, { params });
  if (!response.success || !response.data) {
    throw new Error(response.message || '获取使用统计失败');
  }
  return response as UsageStatsResponse;
};

// 启用AI配置
export const activateConfig = async (configId: string, data: ActivateConfigRequest): Promise<ApiResponse> => {
  return await request.post(`${API_BASE}/configs/${configId}/activate`, data);
};

// 获取当前启用的配置
export const getActiveConfig = async (): Promise<ActiveConfigResponse> => {
  const response = await request.get(`${API_BASE}/active-config`);
  if (!response.success) {
    throw new Error(response.message || '获取启用配置失败');
  }
  return response as ActiveConfigResponse;
};

// 禁用当前配置
export const deactivateConfig = async (): Promise<ApiResponse> => {
  return await request.delete(`${API_BASE}/active-config`);
};

// 获取配置的可用模型
export const getConfigModels = async (configId: string): Promise<ApiResponse<AIServiceModel[]>> => {
  return await request.get(`${API_BASE}/configs/${configId}/models`);
};

// 获取启用配置统计
export const getActiveConfigStats = async (): Promise<ActiveConfigStatsResponse> => {
  const response = await request.get(`${API_BASE}/active-config/stats`);
  if (!response.success || !response.data) {
    throw new Error(response.message || '获取启用配置统计失败');
  }
  return response as ActiveConfigStatsResponse;
};

// 手动添加模型
export const addManualModels = async (
  configId: string,
  models: ManualModelRequest[]
): Promise<ManualModelResponse> => {
  const response = await request.post(
    `${API_BASE}/configs/${configId}/models/manual`,
    { models }
  );
  if (!response.success) {
    throw new Error(response.message || '添加模型失败');
  }
  return response as ManualModelResponse;
};

// 删除手动添加的模型
export const deleteManualModel = async (
  configId: string,
  modelId: string
): Promise<ApiResponse> => {
  const response = await request.delete(
    `${API_BASE}/configs/${configId}/models/${modelId}/manual`
  );
  if (!response.success) {
    throw new Error(response.message || '删除模型失败');
  }
  return response;
};

// 导出所有服务
export const aiConfigService = {
  getConfigs,
  getConfig,
  createConfig,
  updateConfig,
  deleteConfig,
  testConfig,
  setDefaultConfig,
  syncModels,
  getModels,
  updateModel,
  getUsageStats,
  activateConfig,
  getActiveConfig,
  deactivateConfig,
  getConfigModels,
  getActiveConfigStats,
  addManualModels,
  deleteManualModel,
};

export default aiConfigService;
