import React, { useState, useEffect } from 'react';
import {
  ArrowPathIcon,
  PencilIcon,
  CheckCircleIcon,
  XCircleIcon,
  CurrencyDollarIcon,
  XMarkIcon,
  StarIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { AIServiceConfig, AIServiceModel, ManualModelRequest } from '../../../types/aiConfig';
import { aiConfigService } from '../../../services/aiConfigService';
import { useToast } from '../../../hooks/useToast';
// 移除本地模型Token的特殊处理逻辑，统一通过后端同步
import ManualModelDialog from './ManualModelDialog';

interface ModelListProps {
  configs: AIServiceConfig[];
  onRefresh?: () => void;
}

const ModelList: React.FC<ModelListProps> = ({ configs, onRefresh }) => {
  const [selectedConfigId, setSelectedConfigId] = useState<string>('');
  const [allModels, setAllModels] = useState<AIServiceModel[]>([]);
  const [displayModels, setDisplayModels] = useState<AIServiceModel[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [page, setPage] = useState<number>(1);
  const [limit, setLimit] = useState<number>(20);
  const [search, setSearch] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [disabling, setDisabling] = useState(false);
  const [togglingStatus, setTogglingStatus] = useState<string | null>(null);
  const [activatingModel, setActivatingModel] = useState<string | null>(null);
  const [editingModel, setEditingModel] = useState<AIServiceModel | null>(null);
  const [showManualDialog, setShowManualDialog] = useState(false);
  const [addingManual, setAddingManual] = useState(false);
  const [currentModelId, setCurrentModelId] = useState<string | null>(null);
  const { showToast } = useToast();

  // 加载模型列表
  const loadModels = async (configId: string) => {
    if (!configId) {
      setAllModels([]);
      setDisplayModels([]);
      setTotal(0);
      return;
    }

    try {
      setLoading(true);
      // 为了保证“当前模型/已启用模型”跨分页置顶，这里一次性拉取较大数量并在前端分页
      const response = await aiConfigService.getModels({ configId, page: 1, limit: 10000, search: search || undefined });
      const list: AIServiceModel[] = response.data.models || [];

      // 排序：已启用优先，其次将“当前模型”置顶（使用 displayName 上的“当前”标记或 isActive + 后端Active配置对比）
      // 这里从后端统计接口获取当前启用模型ID以准确置顶
      let activeModelId: string | null = null;
      try {
        const active = await aiConfigService.getActiveConfig();
        if (active?.data && (active.data as any).modelId && (active.data as any).configId && (active.data as any).configId._id === configId) {
          activeModelId = ((active.data as any).modelId as any)._id || null;
          setCurrentModelId(activeModelId);
        }
      } catch {}

      const sorted = [...list].sort((a, b) => {
        // 当前模型最上
        if (activeModelId) {
          if (a._id === activeModelId && b._id !== activeModelId) return -1;
          if (b._id === activeModelId && a._id !== activeModelId) return 1;
        }
        // 启用优先
        if (a.isActive !== b.isActive) return a.isActive ? -1 : 1;
        // 其他按显示名
        return (a.displayName || a.modelName).localeCompare(b.displayName || b.modelName);
      });

      setAllModels(sorted);
      setTotal(sorted.length);
    } catch (error) {
      showToast('加载模型列表失败', 'error');
      console.error('加载模型失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 同步模型
  const handleSyncModels = async () => {
    if (!selectedConfigId) return;

    try {
      setSyncing(true);
      await aiConfigService.syncModels(selectedConfigId);
      showToast('模型同步成功', 'success');
      await loadModels(selectedConfigId);
    } catch (error) {
      showToast('模型同步失败', 'error');
      console.error('同步模型失败:', error);
    } finally {
      setSyncing(false);
    }
  };

  // 手动添加模型
  const handleAddManualModels = async (modelsData: ManualModelRequest[]) => {
    if (!selectedConfigId) return;

    try {
      setAddingManual(true);
      await aiConfigService.addManualModels(selectedConfigId, modelsData);
      showToast(`成功添加 ${modelsData.length} 个模型`, 'success');
      await loadModels(selectedConfigId);
    } catch (error) {
      showToast('添加模型失败', 'error');
      console.error('添加模型失败:', error);
      throw error; // 重新抛出错误，让对话框处理
    } finally {
      setAddingManual(false);
    }
  };

  // 删除手动添加的模型
  const handleDeleteManualModel = async (model: AIServiceModel) => {
    if (!selectedConfigId || model.source !== 'manual') return;

    if (!window.confirm(`确定要删除模型 "${model.displayName}" 吗？`)) {
      return;
    }

    try {
      await aiConfigService.deleteManualModel(selectedConfigId, model._id);
      showToast('模型删除成功', 'success');
      await loadModels(selectedConfigId);
    } catch (error) {
      showToast('删除模型失败', 'error');
      console.error('删除模型失败:', error);
    }
  };

  // 一键全部禁用模型
  const handleDisableAllModels = async () => {
    if (!selectedConfigId) return;

    // 确认对话框
    if (!window.confirm('确定要禁用所有模型吗？此操作将禁用当前配置下的所有模型。')) {
      return;
    }

    try {
      setDisabling(true);
      const response = await fetch(`/api/admin/model-pricing/disable-all/${selectedConfigId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('禁用模型失败');
      }

      const result: any = await response.json();
      showToast(`成功禁用 ${result?.data?.disabledCount ?? 0} 个模型`, 'success');
      await loadModels(selectedConfigId);
    } catch (error) {
      console.error('禁用模型失败:', error);
      showToast('禁用模型失败', 'error');
    } finally {
      setDisabling(false);
    }
  };

  // 切换模型状态
  const handleToggleModelStatus = async (model: AIServiceModel) => {
    try {
      setTogglingStatus(model._id);
      const response = await fetch(`/api/admin/model-pricing/toggle-status/${model._id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('切换模型状态失败');
      }

      const result = await response.json();
      showToast(result.message, 'success');
      await loadModels(selectedConfigId);
    } catch (error) {
      console.error('切换模型状态失败:', error);
      showToast('切换模型状态失败', 'error');
    } finally {
      setTogglingStatus(null);
    }
  };

  // 设为当前模型
  const handleActivateModel = async (model: AIServiceModel) => {
    if (!model.isActive) {
      showToast('请先启用该模型', 'error');
      return;
    }

    try {
      setActivatingModel(model._id);
      const response = await fetch(`/api/admin/ai/configs/${selectedConfigId}/activate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: JSON.stringify({
          modelId: model._id
        })
      });

      if (!response.ok) {
        throw new Error('设置当前模型失败');
      }

      const result = await response.json();
      showToast('已设为当前模型', 'success');
      setCurrentModelId(model._id);
      await loadModels(selectedConfigId);
    } catch (error) {
      console.error('设置当前模型失败:', error);
      showToast('设置当前模型失败', 'error');
    } finally {
      setActivatingModel(null);
    }
  };

  // 更新模型
  const handleUpdateModel = async (model: AIServiceModel, updates: Partial<AIServiceModel>) => {
    try {
      await aiConfigService.updateModel(model._id, updates);
      showToast('模型更新成功', 'success');
      await loadModels(selectedConfigId);
      setEditingModel(null);
    } catch (error) {
      showToast('模型更新失败', 'error');
      console.error('更新模型失败:', error);
    }
  };

  useEffect(() => {
    if (configs.length > 0 && !selectedConfigId) {
      const defaultConfig = configs.find(c => c.isDefault) || configs[0];
      setSelectedConfigId(defaultConfig._id);
    }
  }, [configs]);

  useEffect(() => {
    // 选中配置变化时重置分页与搜索
    setPage(1);
    setSearch('');
  }, [selectedConfigId]);

  useEffect(() => {
    loadModels(selectedConfigId);
  }, [selectedConfigId, search]);

  // 根据 page/limit 从 allModels 截取当页展示，并保证当前模型与已启用优先被切片到前端
  useEffect(() => {
    const start = (page - 1) * limit;
    const end = start + limit;
    setDisplayModels(allModels.slice(start, end));
  }, [allModels, page, limit]);

  const selectedConfig = configs.find(c => c._id === selectedConfigId);

  const formatPrice = (pricePerChar: number) => {
    // 统一显示：USD/3000字符
    const pricePer3000 = (pricePerChar * 3000).toFixed(2);
    return `${pricePer3000} USD/3000字符`;
  };

  const getFeatureBadges = (features: string[]) => {
    const colors = {
      text: 'bg-blue-100 text-blue-800',
      image: 'bg-green-100 text-green-800',
      function_calling: 'bg-purple-100 text-purple-800',
      streaming: 'bg-yellow-100 text-yellow-800',
      vision: 'bg-pink-100 text-pink-800',
      audio: 'bg-indigo-100 text-indigo-800'
    };

    return (features as Array<keyof typeof colors>).map((feature) => (
      <span
        key={feature}
        className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
          (colors[feature] as string) || 'bg-gray-100 text-gray-800'
        }`}
      >
        {feature}
      </span>
    ));
  };

  return (
    <div className="space-y-6">
      {/* 配置选择 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              选择配置
            </label>
            <select
              value={selectedConfigId}
              onChange={(e) => setSelectedConfigId(e.target.value)}
              className="input mt-1"
            >
              <option value="">请选择配置</option>
              {configs.map(config => (
                <option key={config._id} value={config._id}>
                  {config.name} ({config.provider})
                </option>
              ))}
            </select>
          </div>
          
          {selectedConfig && (
            <div className="pt-6 flex space-x-3 items-end">
              {/* 本地 Token 输入框已移除，改为同步时弹窗输入 */}
              <button
                onClick={handleSyncModels}
                disabled={syncing || disabling}
                className="btn btn-primary"
              >
                {syncing ? (
                  <>
                    <div className="spinner h-4 w-4 mr-2"></div>
                    同步中...
                  </>
                ) : (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2" />
                    同步模型
                  </>
                )}
              </button>

              {/* 手动添加模型按钮 */}
              {selectedConfig && (
                <button
                  onClick={() => setShowManualDialog(true)}
                  disabled={syncing || disabling || addingManual}
                  className="btn btn-success"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  手动添加模型
                </button>
              )}

              <button
                onClick={handleDisableAllModels}
                disabled={syncing || disabling}
                className="btn btn-secondary"
              >
                {disabling ? (
                  <>
                    <div className="spinner h-4 w-4 mr-2"></div>
                    禁用中...
                  </>
                ) : (
                  <>
                    <XMarkIcon className="h-4 w-4 mr-2" />
                    一键全部禁用
                  </>
                )}
              </button>

              {/* 右侧搜索栏（保留在一键禁用右侧） */}
              <div className="flex items-end space-x-2 ml-4">
                <div>
                  <input
                    type="text"
                    className="input mt-1 w-64"
                    placeholder="按模型名或显示名搜索"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    onKeyDown={async (e) => {
                      if (e.key === 'Enter') {
                        setPage(1);
                        await loadModels(selectedConfigId);
                      }
                    }}
                  />
                </div>
                <button
                  className="btn btn-outline-primary mb-1"
                  onClick={async () => { setPage(1); await loadModels(selectedConfigId); }}
                  disabled={loading}
                >
                  搜索
                </button>
                <button
                  className="btn btn-outline-secondary mb-1"
                  onClick={async () => { setSearch(''); setPage(1); await loadModels(selectedConfigId); }}
                  disabled={loading}
                >
                  清空
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 模型列表 */}
      {selectedConfigId && (
        <div className="card">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="spinner h-8 w-8"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th>模型名称</th>
                    <th>支持功能</th>
                    <th>最大Token</th>
                    <th>定价</th>
                    <th>模型信息</th>
                    <th>状态</th>
                    <th>当前模型</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {displayModels.map((model) => (
                    <tr key={model._id} className="table-row">
                      <td>
                        <div>
                          <div className="font-medium text-gray-900">
                            {model.displayName}
                          </div>
                          <div className="text-sm text-gray-500 font-mono flex items-center">
                            {model.modelName}
                            <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                              model.source === 'manual'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-gray-100 text-gray-600'
                            }`}>
                              {model.source === 'manual' ? '手动' : '同步'}
                            </span>
                          </div>
                          {model.description && (
                            <div className="text-sm text-gray-500 mt-1">
                              {model.description}
                            </div>
                          )}
                        </div>
                      </td>
                      <td>
                        <div className="flex flex-wrap gap-1">
                          {getFeatureBadges(model.supportedFeatures)}
                        </div>
                      </td>
                      <td>
                        <span className="text-sm text-gray-900">
                          {model.maxTokens.toLocaleString()}
                        </span>
                      </td>
                      <td>
                        <div className="text-sm">
                          <div className="flex items-center text-gray-900">
                            <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                            输入: {formatPrice(model.pricing.inputPrice)}
                            {model.isPricingCustomized && (
                              <span className="ml-1 text-xs bg-blue-100 text-blue-800 px-1 rounded" title="手动修改">
                                自定义
                              </span>
                            )}
                          </div>
                          <div className="flex items-center text-gray-500">
                            <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                            输出: {formatPrice(model.pricing.outputPrice)}
                          </div>
                        </div>
                      </td>
                      <td>
                        {model.modelInfo ? (
                          <details className="text-xs">
                            <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                              查看详情
                            </summary>
                            <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-auto max-h-32 max-w-xs">
                              {JSON.stringify(model.modelInfo, null, 2)}
                            </pre>
                          </details>
                        ) : (
                          <span className="text-gray-400 text-xs">无信息</span>
                        )}
                      </td>
                      <td>
                        <button
                          onClick={() => handleToggleModelStatus(model)}
                          disabled={togglingStatus === model._id}
                          className={`badge cursor-pointer transition-colors duration-200 ${
                            model.isActive
                              ? 'badge-success hover:bg-green-600'
                              : 'badge-danger hover:bg-red-600'
                          } ${togglingStatus === model._id ? 'opacity-50 cursor-not-allowed' : ''}`}
                          title={`点击${model.isActive ? '禁用' : '启用'}模型`}
                        >
                          {togglingStatus === model._id ? (
                            <>
                              <div className="spinner h-3 w-3 mr-1"></div>
                              切换中...
                            </>
                          ) : model.isActive ? (
                            <>
                              <CheckCircleIcon className="h-4 w-4 mr-1" />
                              启用
                            </>
                          ) : (
                            <>
                              <XCircleIcon className="h-4 w-4 mr-1" />
                              禁用
                            </>
                          )}
                        </button>
                      </td>
                      <td>
                        {model.isActive ? (
                          currentModelId === model._id ? (
                            <span className="inline-flex items-center text-green-600 text-sm">
                              <StarIcon className="h-4 w-4 mr-1" /> 已是当前
                            </span>
                          ) : (
                            <button
                              onClick={() => handleActivateModel(model)}
                              disabled={activatingModel === model._id}
                              className="btn btn-sm btn-outline-primary"
                              title="设为当前模型"
                            >
                              {activatingModel === model._id ? (
                                <>
                                  <div className="spinner h-3 w-3 mr-1"></div>
                                  设置中...
                                </>
                              ) : (
                                <>
                                  <StarIcon className="h-4 w-4 mr-1" />
                                  设为当前
                                </>
                              )}
                            </button>
                          )
                        ) : (
                          <span className="text-gray-400 text-sm">需先启用</span>
                        )}
                      </td>
                      <td>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => setEditingModel(model)}
                            className="text-indigo-600 hover:text-indigo-900"
                            title="编辑"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>

                          {/* 删除按钮 - 仅对手动添加的模型显示 */}
                          {model.source === 'manual' && (
                            <button
                              onClick={() => handleDeleteManualModel(model)}
                              className="text-red-600 hover:text-red-900"
                              title="删除手动添加的模型"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {displayModels.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-gray-500">
                    {selectedConfigId ? '暂无模型，请先同步模型列表' : '请选择配置'}
                  </div>
                </div>
              )}

              {/* 分页组件 */}
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-600">
                  共 {total} 条记录，第 {total > 0 ? page : 0} / {Math.max(1, Math.ceil(total / limit))} 页
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm text-gray-600">每页</label>
                  <select
                    className="input"
                    value={limit}
                    onChange={async (e) => {
                      const v = parseInt(e.target.value);
                      setLimit(v);
                      setPage(1);
                      await loadModels(selectedConfigId);
                    }}
                  >
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                  </select>

                  <button
                    className="btn btn-outline-secondary"
                    onClick={async () => { if (page > 1) { setPage(page - 1); await loadModels(selectedConfigId); } }}
                    disabled={loading || page <= 1}
                  >
                    上一页
                  </button>
                  <button
                    className="btn btn-outline-secondary"
                    onClick={async () => { const totalPages = Math.max(1, Math.ceil(total / limit)); if (page < totalPages) { setPage(page + 1); await loadModels(selectedConfigId); } }}
                    disabled={loading || page >= Math.max(1, Math.ceil(total / limit))}
                  >
                    下一页
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 编辑模型对话框 */}
      {editingModel && (
        <ModelEditDialog
          model={editingModel}
          onSave={(updates) => handleUpdateModel(editingModel, updates)}
          onCancel={() => setEditingModel(null)}
        />
      )}

      {/* 手动添加模型对话框 */}
      <ManualModelDialog
        isOpen={showManualDialog}
        onClose={() => setShowManualDialog(false)}
        onSubmit={handleAddManualModels}
        configName={selectedConfig?.name || ''}
      />
    </div>
  );
};

// 模型编辑对话框组件
  const ModelEditDialog: React.FC<{
  model: AIServiceModel;
  onSave: (updates: Partial<AIServiceModel>) => void;
  onCancel: () => void;
}> = ({ model, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    displayName: model.displayName,
    description: model.description || '',
    maxTokens: model.maxTokens,
    // 编辑时展示为 USD/3000字符，需要从每字符换算为每3000字符
    inputPricePer3000: Number((model.pricing.inputPrice * 3000).toFixed(4)),
    outputPricePer3000: Number((model.pricing.outputPrice * 3000).toFixed(4)),
    isActive: model.isActive
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      displayName: formData.displayName,
      description: formData.description || undefined,
      maxTokens: formData.maxTokens,
      pricing: {
        ...model.pricing,
        // 保存时从 USD/3000字符 换算回 每字符，并强制 USD
        inputPrice: Number((formData.inputPricePer3000 / 3000).toFixed(8)),
        outputPrice: Number((formData.outputPricePer3000 / 3000).toFixed(8)),
        currency: 'USD'
      },
      isActive: formData.isActive
    });
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          编辑模型 - {model.modelName}
        </h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">显示名称</label>
            <input
              type="text"
              value={formData.displayName}
              onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
              className="input mt-1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">描述</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="input mt-1"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">最大Token</label>
              <input
                type="number"
                value={formData.maxTokens}
                onChange={(e) => setFormData(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                className="input mt-1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">状态</label>
              <select
                value={formData.isActive ? 'active' : 'inactive'}
                onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.value === 'active' }))}
                className="input mt-1"
              >
                <option value="active">启用</option>
                <option value="inactive">禁用</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">输入价格（USD/3000字符）</label>
              <input
                type="number"
                step="0.0001"
                value={formData.inputPricePer3000}
                onChange={(e) => setFormData(prev => ({ ...prev, inputPricePer3000: parseFloat(e.target.value) }))}
                className="input mt-1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">输出价格（USD/3000字符）</label>
              <input
                type="number"
                step="0.0001"
                value={formData.outputPricePer3000}
                onChange={(e) => setFormData(prev => ({ ...prev, outputPricePer3000: parseFloat(e.target.value) }))}
                className="input mt-1"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button type="button" onClick={onCancel} className="btn btn-secondary">
              取消
            </button>
            <button type="submit" className="btn btn-primary">
              保存
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ModelList;
