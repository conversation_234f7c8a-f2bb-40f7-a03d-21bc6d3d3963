import React, { useState } from 'react';
import { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { ManualModelRequest } from '../../../types/aiConfig';
import { useToast } from '../../../hooks/useToast';

interface ManualModelDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (models: ManualModelRequest[]) => Promise<void>;
  configName: string;
}

// 预设模型模板
const MODEL_TEMPLATES = [
  {
    name: 'GPT-4o Mini',
    modelName: 'gpt-4o-mini',
    displayName: 'GPT-4o Mini',
    description: 'OpenAI GPT-4o Mini 模型',
    maxTokens: 128000,
    supportedFeatures: ['text', 'vision', 'function_calling'],
    pricing: { inputPrice: 0.15, outputPrice: 0.6, currency: 'USD' }
  },
  {
    name: 'GPT-4o',
    modelName: 'gpt-4o',
    displayName: 'GPT-4o',
    description: 'OpenAI GPT-4o 模型',
    maxTokens: 128000,
    supportedFeatures: ['text', 'vision', 'function_calling'],
    pricing: { inputPrice: 5, outputPrice: 15, currency: 'USD' }
  },
  {
    name: 'Claude-3.5 Sonnet',
    modelName: 'claude-3-5-sonnet-20241022',
    displayName: 'Claude-3.5 Sonnet',
    description: 'Anthropic Claude-3.5 Sonnet 模型',
    maxTokens: 200000,
    supportedFeatures: ['text', 'vision'],
    pricing: { inputPrice: 3, outputPrice: 15, currency: 'USD' }
  },
  {
    name: 'Gemini Pro',
    modelName: 'gemini-pro',
    displayName: 'Gemini Pro',
    description: 'Google Gemini Pro 模型',
    maxTokens: 32768,
    supportedFeatures: ['text', 'vision'],
    pricing: { inputPrice: 0.5, outputPrice: 1.5, currency: 'USD' }
  }
];

const SUPPORTED_FEATURES = [
  { value: 'text', label: '文本' },
  { value: 'vision', label: '视觉' },
  { value: 'image', label: '图像' },
  { value: 'function_calling', label: '函数调用' },
  { value: 'streaming', label: '流式输出' },
  { value: 'audio', label: '音频' }
];

const ManualModelDialog: React.FC<ManualModelDialogProps> = ({
  isOpen,
  onClose,
  onSubmit,
  configName
}) => {
  const { showToast } = useToast();
  const [models, setModels] = useState<ManualModelRequest[]>([{
    modelName: '',
    displayName: '',
    description: '',
    maxTokens: 4096,
    supportedFeatures: ['text'],
    pricing: {
      inputPrice: 0,
      outputPrice: 0,
      currency: 'USD'
    }
  }]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 添加新模型
  const addModel = () => {
    setModels([...models, {
      modelName: '',
      displayName: '',
      description: '',
      maxTokens: 4096,
      supportedFeatures: ['text'],
      pricing: {
        inputPrice: 0,
        outputPrice: 0,
        currency: 'USD'
      }
    }]);
  };

  // 删除模型
  const removeModel = (index: number) => {
    if (models.length > 1) {
      setModels(models.filter((_, i) => i !== index));
    }
  };

  // 更新模型数据
  const updateModel = (index: number, field: string, value: any) => {
    const updatedModels = [...models];
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      updatedModels[index] = {
        ...updatedModels[index],
        [parent]: {
          ...updatedModels[index][parent as keyof ManualModelRequest],
          [child]: value
        }
      };
    } else {
      updatedModels[index] = {
        ...updatedModels[index],
        [field]: value
      };
    }
    setModels(updatedModels);
  };

  // 应用模板
  const applyTemplate = (templateIndex: number, modelIndex: number) => {
    const template = MODEL_TEMPLATES[templateIndex];
    updateModel(modelIndex, 'modelName', template.modelName);
    updateModel(modelIndex, 'displayName', template.displayName);
    updateModel(modelIndex, 'description', template.description);
    updateModel(modelIndex, 'maxTokens', template.maxTokens);
    updateModel(modelIndex, 'supportedFeatures', template.supportedFeatures);
    updateModel(modelIndex, 'pricing', template.pricing);
  };

  // 处理支持功能变更
  const handleFeatureChange = (modelIndex: number, feature: string, checked: boolean) => {
    const currentFeatures = models[modelIndex].supportedFeatures || [];
    let newFeatures;
    if (checked) {
      newFeatures = [...currentFeatures, feature];
    } else {
      newFeatures = currentFeatures.filter(f => f !== feature);
    }
    updateModel(modelIndex, 'supportedFeatures', newFeatures);
  };

  // 验证表单
  const validateForm = (): boolean => {
    for (let i = 0; i < models.length; i++) {
      const model = models[i];
      if (!model.modelName?.trim()) {
        showToast(`第 ${i + 1} 个模型的名称不能为空`, 'error');
        return false;
      }
    }
    return true;
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setIsSubmitting(true);
      await onSubmit(models);
      onClose();
      // 重置表单
      setModels([{
        modelName: '',
        displayName: '',
        description: '',
        maxTokens: 4096,
        supportedFeatures: ['text'],
        pricing: {
          inputPrice: 0,
          outputPrice: 0,
          currency: 'USD'
        }
      }]);
    } catch (error) {
      console.error('添加模型失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h3 className="text-lg font-medium text-gray-900">手动添加模型</h3>
            <p className="mt-1 text-sm text-gray-500">
              为配置 "{configName}" 添加自定义模型
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {models.map((model, index) => (
            <div key={index} className="mb-8 p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-md font-medium text-gray-900">
                  模型 {index + 1}
                </h4>
                <div className="flex items-center space-x-2">
                  {/* 模板选择 */}
                  <select
                    onChange={(e) => {
                      if (e.target.value) {
                        applyTemplate(parseInt(e.target.value), index);
                        e.target.value = '';
                      }
                    }}
                    className="text-sm border border-gray-300 rounded px-2 py-1"
                  >
                    <option value="">选择模板</option>
                    {MODEL_TEMPLATES.map((template, i) => (
                      <option key={i} value={i}>{template.name}</option>
                    ))}
                  </select>
                  
                  {models.length > 1 && (
                    <button
                      onClick={() => removeModel(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {/* 基本信息 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    模型名称 *
                  </label>
                  <input
                    type="text"
                    value={model.modelName}
                    onChange={(e) => updateModel(index, 'modelName', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="如: gpt-4o-mini"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    显示名称
                  </label>
                  <input
                    type="text"
                    value={model.displayName || ''}
                    onChange={(e) => updateModel(index, 'displayName', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    placeholder="如: GPT-4o Mini"
                  />
                </div>

                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    描述
                  </label>
                  <textarea
                    value={model.description || ''}
                    onChange={(e) => updateModel(index, 'description', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    rows={2}
                    placeholder="模型描述..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    最大Token数
                  </label>
                  <input
                    type="number"
                    value={model.maxTokens}
                    onChange={(e) => updateModel(index, 'maxTokens', parseInt(e.target.value) || 4096)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    min="1"
                    max="1000000"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    货币
                  </label>
                  <select
                    value={model.pricing?.currency || 'USD'}
                    onChange={(e) => updateModel(index, 'pricing.currency', e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="USD">USD</option>
                    <option value="CNY">CNY</option>
                    <option value="EUR">EUR</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    输入价格 (每1K tokens)
                  </label>
                  <input
                    type="number"
                    step="0.001"
                    value={model.pricing?.inputPrice || 0}
                    onChange={(e) => updateModel(index, 'pricing.inputPrice', parseFloat(e.target.value) || 0)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    输出价格 (每1K tokens)
                  </label>
                  <input
                    type="number"
                    step="0.001"
                    value={model.pricing?.outputPrice || 0}
                    onChange={(e) => updateModel(index, 'pricing.outputPrice', parseFloat(e.target.value) || 0)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                    min="0"
                  />
                </div>
              </div>

              {/* 支持功能 */}
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  支持功能
                </label>
                <div className="grid grid-cols-3 gap-2">
                  {SUPPORTED_FEATURES.map((feature) => (
                    <label key={feature.value} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={(model.supportedFeatures || []).includes(feature.value)}
                        onChange={(e) => handleFeatureChange(index, feature.value, e.target.checked)}
                        className="mr-2"
                      />
                      <span className="text-sm">{feature.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          ))}

          {/* 添加更多模型按钮 */}
          <button
            onClick={addModel}
            className="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-gray-500 hover:border-gray-400 hover:text-gray-600 flex items-center justify-center"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            添加更多模型
          </button>
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {isSubmitting ? '添加中...' : `添加 ${models.length} 个模型`}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ManualModelDialog;
