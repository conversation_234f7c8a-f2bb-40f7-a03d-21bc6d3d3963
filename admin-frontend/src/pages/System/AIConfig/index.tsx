import React, { useState, useEffect } from 'react';
import { PlusIcon, CogIcon, PlayIcon, TrashIcon, PencilIcon } from '@heroicons/react/24/outline';
import { CheckCircleIcon, XCircleIcon, ClockIcon } from '@heroicons/react/24/solid';
import ConfigList from './ConfigList';
import ConfigForm from './ConfigForm';
import TestConnection from './TestConnection';
import ModelList from './ModelList';
import UsageStats from './UsageStats';
import { AIServiceConfig } from '../../../types/aiConfig';
import { aiConfigService } from '../../../services/aiConfigService';
import { useToast } from '../../../hooks/useToast';

type TabType = 'configs' | 'models' | 'stats';

const AIConfigPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('configs');
  const [configs, setConfigs] = useState<AIServiceConfig[]>([]);
  const [selectedConfig, setSelectedConfig] = useState<AIServiceConfig | null>(null);
  const [showConfigForm, setShowConfigForm] = useState(false);
  const [showTestDialog, setShowTestDialog] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshKey, setRefreshKey] = useState(0);
  const { showToast } = useToast();

  // 加载配置列表
  const loadConfigs = async () => {
    try {
      setLoading(true);
      const response = await aiConfigService.getConfigs({ limit: 100 });

      // 根据实际的响应结构提取配置列表
      const configs = response.configs || response.data?.configs || [];
      setConfigs(configs);
    } catch (error) {
      showToast('加载配置列表失败', 'error');
      console.error('加载配置失败:', error);
      setConfigs([]); // 确保在错误时设置为空数组
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfigs();
  }, [refreshKey]);

  // 创建配置
  const handleCreateConfig = () => {
    setSelectedConfig(null);
    setShowConfigForm(true);
  };

  // 编辑配置
  const handleEditConfig = (config: AIServiceConfig) => {
    setSelectedConfig(config);
    setShowConfigForm(true);
  };

  // 删除配置
  const handleDeleteConfig = async (config: AIServiceConfig) => {
    if (!confirm(`确定要删除配置 "${config.name}" 吗？`)) {
      return;
    }

    try {
      await aiConfigService.deleteConfig(config._id);
      showToast('配置删除成功', 'success');
      setRefreshKey(prev => prev + 1);
    } catch (error) {
      showToast('删除配置失败', 'error');
      console.error('删除配置失败:', error);
    }
  };

  // 测试连接
  const handleTestConfig = (config: AIServiceConfig) => {
    setSelectedConfig(config);
    setShowTestDialog(true);
  };

  // 移除“设为默认”逻辑：启用即默认

  // 启用配置（这个函数实际上不会被调用，因为启用逻辑在ConfigList内部处理）
  const handleActivateConfig = (config: AIServiceConfig) => {
    // 这个函数主要是为了满足接口要求，实际逻辑在ConfigList中
    console.log('启用配置:', config.name);
  };

  // 配置表单提交成功
  const handleConfigFormSuccess = () => {
    setShowConfigForm(false);
    setSelectedConfig(null);
    setRefreshKey(prev => prev + 1);
  };

  const tabs = [
    { id: 'configs', name: 'AI配置管理', icon: CogIcon },
    { id: 'models', name: '模型管理', icon: PlayIcon },
    { id: 'stats', name: '使用统计', icon: ClockIcon },
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">AI服务配置管理</h1>
          <p className="mt-1 text-sm text-gray-500">
            管理AI服务配置、模型和使用统计
          </p>
        </div>
        {activeTab === 'configs' && (
          <button
            onClick={handleCreateConfig}
            className="btn btn-primary"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            新增配置
          </button>
        )}
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TabType)}
                className={`
                  flex items-center py-2 px-1 border-b-2 font-medium text-sm
                  ${activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-5 w-5 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </nav>
      </div>

      {/* 标签页内容 */}
      <div className="mt-6">
        {activeTab === 'configs' && (
          <ConfigList
            configs={configs}
            loading={loading}
            onEdit={handleEditConfig}
            onDelete={handleDeleteConfig}
            onTest={handleTestConfig}
            onActivate={handleActivateConfig}
            onRefresh={() => setRefreshKey(prev => prev + 1)}
          />
        )}

        {activeTab === 'models' && (
          <ModelList
            configs={configs}
            onRefresh={() => setRefreshKey(prev => prev + 1)}
          />
        )}

        {activeTab === 'stats' && (
          <UsageStats
            configs={configs}
          />
        )}
      </div>

      {/* 配置表单对话框 */}
      {showConfigForm && (
        <ConfigForm
          config={selectedConfig}
          onSuccess={handleConfigFormSuccess}
          onCancel={() => {
            setShowConfigForm(false);
            setSelectedConfig(null);
          }}
        />
      )}

      {/* 测试连接对话框 */}
      {showTestDialog && selectedConfig && (
        <TestConnection
          config={selectedConfig}
          onClose={() => {
            setShowTestDialog(false);
            setSelectedConfig(null);
          }}
        />
      )}
    </div>
  );
};

export default AIConfigPage;
