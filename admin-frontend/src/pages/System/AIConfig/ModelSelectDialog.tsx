import React, { useState, useEffect } from 'react';
import { XMarkIcon, CheckIcon } from '@heroicons/react/24/outline';
import { AIServiceConfig, AIServiceModel } from '../../../types/aiConfig';
import { aiConfigService } from '../../../services/aiConfigService';
import { useToast } from '../../../hooks/useToast';

interface ModelSelectDialogProps {
  config: AIServiceConfig;
  onSuccess: () => void;
  onCancel: () => void;
}

const ModelSelectDialog: React.FC<ModelSelectDialogProps> = ({ config, onSuccess, onCancel }) => {
  const [models, setModels] = useState<AIServiceModel[]>([]);
  const [selectedModelId, setSelectedModelId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [activating, setActivating] = useState(false);
  const { showToast } = useToast();

  // 加载模型列表
  useEffect(() => {
    const loadModels = async () => {
      try {
        setLoading(true);
        const response = await aiConfigService.getConfigModels(config._id);
        setModels(response.data || []);
        
        // 默认选择第一个模型
        if (response.data && response.data.length > 0) {
          setSelectedModelId(response.data[0]._id);
        }
      } catch (error) {
        showToast('加载模型列表失败', 'error');
        console.error('加载模型失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadModels();
  }, [config._id, showToast]);

  // 启用配置
  const handleActivate = async () => {
    if (!selectedModelId) {
      showToast('请选择一个模型', 'error');
      return;
    }

    try {
      setActivating(true);
      await aiConfigService.activateConfig(config._id, { modelId: selectedModelId });
      showToast('配置启用成功', 'success');
      onSuccess();
    } catch (error) {
      showToast('启用配置失败', 'error');
      console.error('启用配置失败:', error);
    } finally {
      setActivating(false);
    }
  };

  const formatPrice = (pricePerChar: number) => {
    if (pricePerChar === 0) return '免费';
    return `${(pricePerChar * 3000).toFixed(2)} USD/3000字符`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h3 className="text-lg font-medium text-gray-900">启用AI配置</h3>
            <p className="mt-1 text-sm text-gray-500">
              选择要启用的模型：{config.name}
            </p>
          </div>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="spinner h-8 w-8"></div>
              <span className="ml-2 text-gray-500">加载模型列表...</span>
            </div>
          ) : models.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">该配置下没有可用的模型</p>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-gray-600 mb-4">
                请选择要启用的模型。启用后，系统将使用此配置和模型进行AI调用。
              </p>
              
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {models.map((model) => (
                  <div
                    key={model._id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedModelId === model._id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedModelId(model._id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <div className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${
                            selectedModelId === model._id
                              ? 'border-blue-500 bg-blue-500'
                              : 'border-gray-300'
                          }`}>
                            {selectedModelId === model._id && (
                              <CheckIcon className="h-3 w-3 text-white" />
                            )}
                          </div>
                          <h4 className="font-medium text-gray-900">{model.displayName}</h4>
                        </div>
                        
                        <p className="text-sm text-gray-600 mt-1 ml-7">
                          模型ID: {model.modelName}
                        </p>
                        
                        {model.description && (
                          <p className="text-sm text-gray-500 mt-1 ml-7">
                            {model.description}
                          </p>
                        )}
                        
                        <div className="flex items-center space-x-4 mt-2 ml-7 text-xs text-gray-500">
                          <span>最大令牌: {model.maxTokens.toLocaleString()}</span>
                          <span>
                            输入价格: {formatPrice(model.pricing.inputPrice)}
                          </span>
                          <span>
                            输出价格: {formatPrice(model.pricing.outputPrice)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onCancel}
            className="btn btn-secondary"
            disabled={activating}
          >
            取消
          </button>
          <button
            onClick={handleActivate}
            className="btn btn-primary"
            disabled={loading || models.length === 0 || !selectedModelId || activating}
          >
            {activating ? (
              <>
                <div className="spinner h-4 w-4 mr-2"></div>
                启用中...
              </>
            ) : (
              '启用配置'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ModelSelectDialog;
