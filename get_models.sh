#!/bin/bash

# 获取模型列表的测试脚本

API_TOKEN="sk-aOxnzCfQjl69VGHV18578aFf7fC74cEcB896EcE8Ed8553E2"
BASE_URL="http://localhost:3000"

echo "=== 获取模型列表 ==="
echo "API Token: ${API_TOKEN}"
echo "Base URL: ${BASE_URL}"
echo ""

# 获取模型列表 (OpenAI 兼容格式)
echo "1. 获取模型列表 (OpenAI 兼容)..."
curl -X GET "${BASE_URL}/v1/models" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -v

echo -e "\n\n=== 分隔线 ===\n"

# 格式化输出 (如果安装了 jq)
echo "2. 格式化输出模型列表..."
curl -X GET "${BASE_URL}/v1/models" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -s | jq '.' 2>/dev/null || echo "需要安装 jq 来格式化 JSON 输出"

echo -e "\n\n=== 分隔线 ===\n"

# 只显示模型 ID
echo "3. 只显示模型 ID..."
curl -X GET "${BASE_URL}/v1/models" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -s | jq -r '.data[].id' 2>/dev/null || echo "需要安装 jq 来解析模型 ID"

echo -e "\n=== 完成 ==="
