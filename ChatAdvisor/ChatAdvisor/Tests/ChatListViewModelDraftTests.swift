import XCTest
import Combine
@testable import ChatAdvisor

final class ChatListViewModelDraftTests: XCTestCase {
    var viewModel: ChatListViewModel!
    var cancellables: Set<AnyCancellable>!

    override func setUp() {
        super.setUp()
        viewModel = ChatListViewModel()
        cancellables = []
    }

    override func tearDown() {
        cancellables.forEach { $0.cancel() }
        cancellables = nil
        viewModel = nil
        super.tearDown()
    }

    func testAddDraftChat_ShouldAppearInList() {
        let chat = Chat(id: "draft-1", messages: [], title: "草稿会话")
        let expect = expectation(description: "publish")

        // 订阅以确保发布
        viewModel.$chats.dropFirst().sink { _ in
            expect.fulfill()
        }.store(in: &cancellables)

        Task { @MainActor in
            viewModel.addDraftChat(chat)
        }

        wait(for: [expect], timeout: 1.0)
        XCTAssertTrue(viewModel.chats.contains { $0.id == chat.id })
    }

    func testDraftPromoteOnFirstUserMessage() async {
        // 先创建草稿
        var chat = Chat(id: "draft-2", messages: [], title: "草稿2")
        await MainActor.run { viewModel.addDraftChat(chat) }

        // 模拟首条用户消息
        let userMsg = ChatMessage(id: "u1", chatId: chat.id, role: .user, content: "hi")
        chat.messages = [userMsg]
        await MainActor.run { viewModel.updateMemoryChat(newChat: chat) }

        // 应仍在列表中
        XCTAssertTrue(viewModel.chats.contains { $0.id == chat.id })
    }
}


