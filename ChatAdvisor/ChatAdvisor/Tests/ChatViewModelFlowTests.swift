import XCTest
import Combine
@testable import ChatAdvisor

final class ChatViewModelFlowTests: XCTestCase {
    var chatListViewModel: ChatListViewModel!
    var chatViewModel: ChatViewModel!

    override func setUp() {
        super.setUp()
        chatListViewModel = ChatListViewModel()
        chatViewModel = ChatViewModel(chatListViewModel: chatListViewModel)
    }

    override func tearDown() {
        chatViewModel = nil
        chatListViewModel = nil
        super.tearDown()
    }

    func testStartNewChat_ShouldInsertDraftAndPersist() async throws {
        // 启动新会话
        await MainActor.run {
            chatViewModel.startNewChat()
        }

        // 草稿应进入列表
        XCTAssertTrue(chatListViewModel.chats.contains { $0.id == chatViewModel.currentChat.id })
    }

    func testSendFirstMessage_ShouldPromoteDraft() async throws {
        await MainActor.run { chatViewModel.startNewChat() }
        let chatId = chatViewModel.currentChat.id

        await MainActor.run {
            chatViewModel.sendMessage("hello")
        }

        // 发送后，chatList 应包含该会话且包含用户消息
        let found = chatListViewModel.chats.first { $0.id == chatId }
        XCTAssertNotNil(found)
    }
}


