import XCTest
@testable import ChatAdvisor

final class SSEManagerTests: XCTestCase {
    func testDecodeChunk_ShouldParseChoiceDelta() throws {
        // 构造一段SSE数据中单个JSON对象
        let json = """
        {"id":"test-1","object":"chat.completion.chunk","created":1730000000,"model":"gpt-4o-mini","choices":[{"index":0,"delta":{"content":"Hello"},"finish_reason":null}]}
        """
        let data = json.data(using: .utf8)!
        let chunk = try JSONDecoder().decode(ChatCompletionChunk.self, from: data)
        XCTAssertEqual(chunk.id, "test-1")
        XCTAssertEqual(chunk.choices.first?.delta?.content, "Hello")
    }
}


