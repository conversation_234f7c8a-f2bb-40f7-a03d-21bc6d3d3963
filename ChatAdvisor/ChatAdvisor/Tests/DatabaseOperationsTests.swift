import XCTest
@testable import ChatAdvisor
import WCDBSwift

final class DatabaseOperationsTests: XCTestCase {
    var dbPath: String!
    var database: Database!

    override func setUpWithError() throws {
        dbPath = (NSTemporaryDirectory() as NSString).appendingPathComponent("chatadvisor-db-tests.sqlite")
        // 清理旧库
        try? FileManager.default.removeItem(atPath: dbPath)

        database = Database(at: dbPath)
        // 创建必要的数据表
        try database.create(table: "chats", of: Chat.self)
        try database.create(table: "chatMessages", of: ChatMessage.self)

        // 绑定到应用的数据库管理器
        AdvisorDatabaseManager.shared.database = database
    }

    override func tearDownWithError() throws {
        AdvisorDatabaseManager.shared.database = nil
        try? database.close()
        try? FileManager.default.removeItem(atPath: dbPath)
    }

    func testInsertFetchUpdateChat() async throws {
        let chat = Chat(id: UUID().uuidString, messages: [], title: "测试会话")
        AdvisorDatabaseManager.shared.insert(chat: chat)

        // 等待异步队列完成
        try await Task.sleep(nanoseconds: 200_000_000)

        let fetched = try await AdvisorDatabaseManager.shared.fetchChat(id: chat.id)
        XCTAssertNotNil(fetched)
        XCTAssertEqual(fetched?.id, chat.id)
        XCTAssertEqual(fetched?.title, "测试会话")

        AdvisorDatabaseManager.shared.updateChatTitle(id: chat.id, title: "新标题")
        try await Task.sleep(nanoseconds: 200_000_000)
        let updated = try await AdvisorDatabaseManager.shared.fetchChat(id: chat.id)
        XCTAssertEqual(updated?.title, "新标题")
    }

    func testInsertAndFetchMessages() async throws {
        let chatId = UUID().uuidString
        let chat = Chat(id: chatId, messages: [], title: "消息会话")
        AdvisorDatabaseManager.shared.insert(chat: chat)
        try await Task.sleep(nanoseconds: 150_000_000)

        let msg1 = ChatMessage(id: UUID().uuidString, chatId: chatId, role: .user, content: "Hello")
        let msg2 = ChatMessage(id: UUID().uuidString, chatId: chatId, role: .assistant, content: "Hi")
        AdvisorDatabaseManager.shared.insert(message: msg1)
        AdvisorDatabaseManager.shared.insert(message: msg2)

        try await Task.sleep(nanoseconds: 200_000_000)
        let fetched = await AdvisorDatabaseManager.shared.fetchMessages(chatId: chatId, offset: 0, limit: 10)
        XCTAssertEqual(fetched.count, 2)
        XCTAssertEqual(fetched.first?.id, msg1.id)
        XCTAssertEqual(fetched.last?.id, msg2.id)
    }
}


