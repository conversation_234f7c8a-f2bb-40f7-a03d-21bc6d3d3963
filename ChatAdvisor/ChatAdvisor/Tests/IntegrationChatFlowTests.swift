import XCTest
import Combine
@testable import ChatAdvisor

final class IntegrationChatFlowTests: XCTestCase {
    var contentViewModel: ContentViewModel!
    var chatListViewModel: ChatListViewModel!

    override func setUp() {
        super.setUp()
        contentViewModel = ContentViewModel()
        chatListViewModel = ChatListViewModel()
    }

    override func tearDown() {
        contentViewModel = nil
        chatListViewModel = nil
        super.tearDown()
    }

    func testNewChatAppearsAndSwitchingIsStable() async throws {
        // 新建一个ChatViewModel并启动新会话
        let chatViewModel = ChatViewModel(chatListViewModel: chatListViewModel)
        await MainActor.run {
            chatViewModel.startNewChat()
        }

        let newId = chatViewModel.currentChat.id
        // 确认会话立即出现在列表
        XCTAssertTrue(chatListViewModel.chats.contains { $0.id == newId })

        // 选择该会话
        try await contentViewModel.selectChatAtomically(chat: Chat(id: newId, messages: [], title: ""), chatListViewModel: chatListViewModel)
        XCTAssertEqual(contentViewModel.selectedChatID, newId)

        // 再切换到另一个会话再切回
        let another = Chat(id: "another-1", messages: [], title: "")
        try await contentViewModel.selectChatAtomically(chat: another, chatListViewModel: chatListViewModel)
        try await contentViewModel.selectChatAtomically(chat: Chat(id: newId, messages: [], title: ""), chatListViewModel: chatListViewModel)

        XCTAssertEqual(contentViewModel.selectedChatID, newId)
        XCTAssertNotNil(contentViewModel.currentChatViewModel)
    }
}


