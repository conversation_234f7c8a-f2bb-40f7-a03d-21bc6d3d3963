//
//  SSEStreamingOptimizationTests.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025-01-03.
//

import XCTest
import Combine
@testable import ChatAdvisor

class SSEStreamingOptimizationTests: XCTestCase {
    var chatViewModel: ChatViewModel!
    var cancellables: Set<AnyCancellable>!

    override func setUp() {
        super.setUp()
        chatViewModel = ChatViewModel(chatListViewModel: nil)
        cancellables = Set<AnyCancellable>()
    }

    override func tearDown() {
        chatViewModel = nil
        cancellables = nil
        super.tearDown()
    }

    // MARK: - ChatViewModel Tests

    func testStreamingMessageDisplayContent() {
        // {{ AURA-X: Simplify - 测试简化后的消息显示逻辑 }}
        let testMessage = ChatMessage(
            id: "test-message",
            chatId: "test-chat",
            role: .assistant,
            content: "Hello, <PERSON>!",
            isComplete: false
        )

        // 添加消息到当前聊天
        chatViewModel.currentChat.messages.append(testMessage)

        // 测试获取显示内容
        let displayContent = chatViewModel.getStreamingMessageDisplayContent(for: testMessage.id)
        XCTAssertEqual(displayContent, "Hello, World!")
    }

    func testStreamingMessageDisplayContentForNonExistentMessage() {
        // 测试不存在的消息ID
        let displayContent = chatViewModel.getStreamingMessageDisplayContent(for: "non-existent-id")
        XCTAssertEqual(displayContent, "")
    }

    // MARK: - ChatViewModel Integration Tests

    func testChatViewModelStreamingMessageDisplayContent() {
        // {{ AURA-X: Simplify - 测试简化后的ChatViewModel集成 }}
        // 创建测试消息
        let testMessage = ChatMessage(
            id: "test-message",
            chatId: "test-chat",
            role: .assistant,
            content: "Test streaming content",
            isComplete: false
        )

        // 添加到当前聊天
        chatViewModel.currentChat.messages.append(testMessage)

        // 模拟设置当前流式消息ID
        chatViewModel.currentLocalResponseId = "test-message"

        // 测试获取显示内容
        let displayContent = chatViewModel.getStreamingMessageDisplayContent(for: "test-message")

        // 现在直接返回消息内容
        XCTAssertEqual(displayContent, testMessage.content)
    }

    // MARK: - Performance Tests

    func testStreamingMessagePerformance() {
        // {{ AURA-X: Simplify - 测试简化后的流式消息性能 }}
        let largeContent = String(repeating: "This is a performance test content. ", count: 100)

        let testMessage = ChatMessage(
            id: "perf-test-message",
            chatId: "test-chat",
            role: .assistant,
            content: largeContent,
            isComplete: false
        )

        chatViewModel.currentChat.messages.append(testMessage)

        measure {
            let _ = chatViewModel.getStreamingMessageDisplayContent(for: testMessage.id)
        }
    }

    // MARK: - Edge Cases Tests

    func testStreamingMessageWithEmptyContent() {
        // {{ AURA-X: Simplify - 测试空内容的处理 }}
        let emptyMessage = ChatMessage(
            id: "empty-message",
            chatId: "test-chat",
            role: .assistant,
            content: "",
            isComplete: false
        )

        chatViewModel.currentChat.messages.append(emptyMessage)

        let displayContent = chatViewModel.getStreamingMessageDisplayContent(for: emptyMessage.id)
        XCTAssertEqual(displayContent, "")
    }

    func testStreamingMessageWithSpecialCharacters() {
        // {{ AURA-X: Simplify - 测试特殊字符的处理 }}
        let specialContent = "Hello! 你好？ 🌟 \n\t Special chars: @#$%^&*()"

        let specialMessage = ChatMessage(
            id: "special-message",
            chatId: "test-chat",
            role: .assistant,
            content: specialContent,
            isComplete: false
        )

        chatViewModel.currentChat.messages.append(specialMessage)

        let displayContent = chatViewModel.getStreamingMessageDisplayContent(for: specialMessage.id)
        XCTAssertEqual(displayContent, specialContent)
    }

    // MARK: - SSE Data Fragmentation Tests

    func testJSONFragmentationHandling() {
        // {{ AURA-X: Add - 测试JSON数据分片处理 }}
        let sseManager = URLSessionSSEManager<ChatCompletionChunk>()

        // 模拟分片的JSON数据
        let completeJSON = """
        {"choices":[{"delta":{"content":"测试内容"},"finish_reason":null,"index":0,"logprobs":null}],"object":"chat.completion.chunk","usage":null,"created":1754580503,"system_fingerprint":null,"model":"qwen-plus-latest","id":"chatcmpl-test-id"}
        """

        let fragment1 = "data: {\"choices\":[{\"delta\":{\"content\":\"测试内容\"},\"finish_reason\":null,\"index\":0,\"logprobs\":null}],\"object\":\"chat.completion.chunk\",\"usage\":null,\"created\":1754580503,\"system_fingerprint\":null,\"model\":\"qwen-plus-latest\",\"id\":\"chatcmpl-test-"
        let fragment2 = "id\"}\n"

        var receivedData: ChatCompletionChunk?
        let expectation = XCTestExpectation(description: "Should handle fragmented JSON correctly")

        sseManager.onEventReceived = { chunk in
            receivedData = chunk
            expectation.fulfill()
        }

        // 模拟分片数据接收
        if let data1 = fragment1.data(using: .utf8) {
            sseManager.urlSession(URLSession.shared, dataTask: URLSessionDataTask(), didReceive: data1)
        }

        if let data2 = fragment2.data(using: .utf8) {
            sseManager.urlSession(URLSession.shared, dataTask: URLSessionDataTask(), didReceive: data2)
        }

        wait(for: [expectation], timeout: 5.0)

        XCTAssertNotNil(receivedData)
        XCTAssertEqual(receivedData?.choices.first?.delta?.content, "测试内容")
    }
}
