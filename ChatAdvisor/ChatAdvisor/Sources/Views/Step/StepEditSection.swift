//
//  StepEditSection.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/30.
//  功能：支持预览/编辑模式切换的步骤编辑组件
//

import SwiftUI
import Localize_Swift

struct StepEditSection: View {
    @ObservedObject var viewModel: StepViewModel
    @Binding var isPreviewMode: Bool

    // Add - 自定义字段可编辑行模型，支持就地编辑、重命名与删除
    private struct EditableCustomField: Identifiable, Hashable {
        let id: UUID
        var originalKey: String? // 已提交到 viewModel 的原始键，用于重命名对照
        var key: String
        var value: String
        var isCommitted: Bool
    }
    @State private var customFieldRows: [EditableCustomField] = []
    
    var body: some View {
        VStack(alignment: .leading, spacing: AppThemes.padding) {
            // 步骤标题头部
            StepHeaderView(stepType: viewModel.stepType, isComplete: viewModel.isComplete)
            
            // 根据模式显示不同内容
            if isPreviewMode {
                // 预览模式：显示已填写的内容
                PreviewContent()
            } else {
                // 编辑模式：显示可编辑表单
                EditingContent()
            }
        }
        .padding(AppThemes.padding)
        .background(
            RoundedRectangle(cornerRadius: AppThemes.cornerRadius)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .animation(.easeInOut(duration: 0.3), value: isPreviewMode)
    }
    
    // MARK: - 预览内容视图
    @ViewBuilder
    private func PreviewContent() -> some View {
        if viewModel.stepType == .import {
            // 导入步骤的预览
            if !viewModel.recognizeViewModel.recognizedTextsByImage.isEmpty {
                VStack(alignment: .leading, spacing: AppThemes.padding / 2) {
                    Text("step_imported_chat_records".localized())
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("\(viewModel.recognizeViewModel.recognizedTextsByImage.count) 条记录")
                        .font(.caption)
                        .foregroundColor(.mainDark)
                }
            } else {
                Text("step_no_chat_records".localized())
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        } else {
            // 标准字段和自定义字段的预览
            VStack(alignment: .leading, spacing: AppThemes.padding / 2) {
                // 显示标准字段
                PreviewStandardFields()
                
                // 显示自定义字段
                if !viewModel.customFields.isEmpty {
                    Divider()
                    PreviewCustomFields()
                }
                
                // 如果没有任何内容
                if viewModel.fields.values.allSatisfy(\.isEmpty) && viewModel.customFields.isEmpty {
                    Text("step_no_content".localized())
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .italic()
                }
            }
        }
    }
    
    // MARK: - 编辑内容视图
    @ViewBuilder
    private func EditingContent() -> some View {
        if viewModel.stepType == .import {
            // 导入步骤：使用现有的 RecognizeView
            RecognizeView(viewModel: viewModel.recognizeViewModel)
        } else {
            VStack(spacing: AppThemes.padding) {
                // 标准字段编辑
                if !viewModel.fields.isEmpty {
                    StandardFieldsEditView()
                }
                
                // 自定义字段编辑
                if viewModel.showCustomFields {
                    CustomFieldsEditView()
                }
            }
        }
    }
    
    // MARK: - 预览模式子视图
    @ViewBuilder
    private func PreviewStandardFields() -> some View {
        ForEach(Array(viewModel.fields.keys).sorted(), id: \.self) { field in
            if let value = viewModel.fields[field], !value.isEmpty {
                HStack {
                    Text(field.localized())
                        .font(.subheadline)
                        .foregroundColor(.primary)
                        .frame(width: 120, alignment: .leading)
                    
                    // {{ AURA-X: Fix - 修复选择器字段在预览时显示正确的本地化值. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
                    Text(getDisplayValue(field: field, value: value))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    Spacer()
                }
            }
        }
    }
    
    // {{ AURA-X: Add - 获取字段的正确显示值，处理选择器的本地化. Approval: mcp-feedback-enhanced(ID:feedback_001). }}
    private func getDisplayValue(field: String, value: String) -> String {
        // 如果是预定义选项字段，返回本地化的值
        if viewModel.predefinedOptions.keys.contains(field) {
            return value.localized()
        }
        // 否则返回原始值的本地化
        return value.localized()
    }
    
    @ViewBuilder
    private func PreviewCustomFields() -> some View {
        // Modify - 使用稳定的多语言key，修复英文环境下未显示的问题。Confirmed via mcp-feedback-enhanced
        Text("custom_fields_title".localized())
            .font(.caption)
            .foregroundColor(.mainDark)
        
        ForEach(Array(viewModel.customFields.keys).sorted(), id: \.self) { key in
            if let value = viewModel.customFields[key], !value.isEmpty {
                HStack {
                    Text(key.localized())
                        .font(.subheadline)
                        .foregroundColor(.primary)
                        .frame(width: 120, alignment: .leading)
                    
                    Text(value.localized())
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    Spacer()
                }
            }
        }
    }
    
    // MARK: - 编辑模式子视图
    @ViewBuilder
    private func StandardFieldsEditView() -> some View {
        VStack(spacing: AppThemes.padding / 2) {
            // 文本输入字段
            ForEach(Array(viewModel.fields.keys.filter { !viewModel.predefinedOptions.keys.contains($0) }).sorted { $0.localized() > $1.localized() }, id: \.self) { field in
                HStack {
                    Text(field.localized())
                        .frame(width: 120, alignment: .leading)
                        .font(.subheadline)
                    
                    TextField("\(field.localized())", text: Binding(
                        get: { viewModel.fields[field] ?? "" },
                        set: { viewModel.setField(field, value: $0) }
                    ))
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                }
            }
            
            // {{ AURA-X: Fix - 修改选择器字段为左右分布布局，与填空字段保持一致. Approval: mcp-feedback-enhanced(ID:feedback_002). }}
            // 选择器字段
            ForEach(Array(viewModel.predefinedOptions.keys).sorted { $0 > $1 }, id: \.self) { field in
                if let options = viewModel.predefinedOptions[field] {
                    HStack {
                        Text(field.localized())
                            .frame(width: 120, alignment: .leading)
                            .font(.subheadline)
                        
                        Picker(field.localized(), selection: Binding<String>(
                            get: { 
                                // 确保返回有效的选择值或空字符串
                                let currentValue = viewModel.fields[field] ?? ""
                                return currentValue.isEmpty ? "" : currentValue
                            },
                            set: { newValue in
                                viewModel.setField(field, value: newValue)
                            }
                        )) {
                            // 空选项
                            Text("Choose Please".localized())
                                .tag("")
                            
                            // 预定义选项
                            ForEach(options, id: \.self) { option in
                                Text(option.localized())
                                    .tag(option)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        .tint(.mainDark)
                        Spacer()
                    }
                }
            }
        }
    }
    
    @ViewBuilder
    private func CustomFieldsEditView() -> some View {
        VStack(alignment: .leading, spacing: AppThemes.padding / 2) {
            // Modify - 使用稳定的多语言key，修复英文环境下未显示的问题。Confirmed via mcp-feedback-enhanced
            // Modify - 仅保留标题的“加号”按钮，点击后新增一条空行，右侧提供删除图标。Confirmed via mcp-feedback-enhanced
            HStack {
                Text("custom_fields_title".localized())
                    .font(.headline)
                    .foregroundColor(.mainDark)
                Spacer()
                Button(action: {
                    withAnimation { addCustomField() }
                }) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.mainDark)
                }
            }

            // 可编辑的自定义字段行
            ForEach(customFieldRows.indices, id: \.self) { index in
                HStack {
                    TextField("custom_field_name_placeholder".localized(), text: Binding(
                        get: { customFieldRows[index].key },
                        set: { newValue in
                            customFieldRows[index].key = newValue
                            commitRowChange(index)
                        }
                    ))
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .frame(width: 120, alignment: .leading)

                    Text(":")

                    TextField("custom_field_value_placeholder".localized(), text: Binding(
                        get: { customFieldRows[index].value },
                        set: { newValue in
                            customFieldRows[index].value = newValue
                            commitRowChange(index)
                        }
                    ))
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(1)

                    Spacer()

                    Button(role: .destructive) {
                        deleteRow(index)
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.red)
                    }
                }
                .padding(.horizontal, AppThemes.padding / 2)
            }

            // 历史字段
            if !viewModel.historicalFields.keys.filter({ !viewModel.customFields.keys.contains($0) }).isEmpty {
                Text("custom_fields_deleted_section_title".localized())
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, AppThemes.padding / 2)

                ForEach(viewModel.historicalFields.keys.filter { !viewModel.customFields.keys.contains($0) }.sorted(), id: \.self) { key in
                    HStack {
                        Text(key)
                            .frame(width: 120, alignment: .leading)
                        Text(":")
                        Text(viewModel.historicalFields[key] ?? "")
                            .lineLimit(1)
                        Spacer()

                        Button("使用".localized()) {
                            let value = viewModel.historicalFields[key] ?? ""
                            viewModel.setCustomField(key, value: value)
                            viewModel.historicalFields.removeValue(forKey: key)
                            if !customFieldRows.contains(where: { $0.originalKey == key }) {
                                customFieldRows.append(EditableCustomField(id: UUID(), originalKey: key, key: key, value: value, isCommitted: true))
                            }
                        }
                        .font(.caption)
                        .foregroundColor(.mainDark)

                        Button(action: {
                            viewModel.historicalFields.removeValue(forKey: key)
                            viewModel.removeCustomField(key)
                            ChatConfigDatabaseManager.shared.removeHistoricalFieldFromDatabase(key)
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.red)
                        }
                    }
                    .padding(.horizontal, AppThemes.padding / 2)
                }
            }
        }
        .onAppear { syncRowsFromViewModel() }
        .onReceive(viewModel.$customFields) { _ in syncRowsFromViewModel(preserveUncommitted: true) }
    }

    // Add - 新增一行空白自定义字段（未提交）
    private func addCustomField() {
        customFieldRows.append(EditableCustomField(id: UUID(), originalKey: nil, key: "", value: "", isCommitted: false))
    }

    // Add - 行内容变化提交到 viewModel（键和值都非空时）
    private func commitRowChange(_ index: Int) {
        guard customFieldRows.indices.contains(index) else { return }
        let trimmedKey = customFieldRows[index].key.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedValue = customFieldRows[index].value.trimmingCharacters(in: .whitespacesAndNewlines)

        guard !trimmedKey.isEmpty, !trimmedValue.isEmpty else {
            customFieldRows[index].isCommitted = false
            return
        }

        if let original = customFieldRows[index].originalKey, original != trimmedKey {
            viewModel.removeCustomField(original)
        }

        viewModel.setCustomField(trimmedKey, value: trimmedValue)
        customFieldRows[index].originalKey = trimmedKey
        customFieldRows[index].isCommitted = true
    }

    // Add - 删除一行；若已提交则同步删除 viewModel 并记录历史
    private func deleteRow(_ index: Int) {
        guard customFieldRows.indices.contains(index) else { return }
        let row = customFieldRows[index]
        if row.isCommitted, let key = row.originalKey {
            viewModel.historicalFields[key] = viewModel.customFields[key]
            viewModel.removeCustomField(key)
        }
        customFieldRows.remove(at: index)
    }

    // Add - 从 viewModel 同步行；可选择保留未提交行
    private func syncRowsFromViewModel(preserveUncommitted: Bool = false) {
        let committedRows = viewModel.customFields.keys.sorted().map { key in
            EditableCustomField(id: UUID(), originalKey: key, key: key, value: viewModel.customFields[key] ?? "", isCommitted: true)
        }
        if preserveUncommitted {
            let uncommitted = customFieldRows.filter { !$0.isCommitted }
            customFieldRows = committedRows + uncommitted
        } else {
            customFieldRows = committedRows
        }
    }
}

// MARK: - 步骤标题头部视图
struct StepHeaderView: View {
    let stepType: StepType
    let isComplete: Bool
    
    var body: some View {
        HStack {
            Image(systemName: iconName)
                .foregroundColor(isComplete ? .green : .mainDark)
                .font(.title2)
            
            Text(stepType.title.localized())
                .font(.headline)
                .foregroundColor(.primary)
            
            Spacer()
            
            // 完成状态指示器
            if isComplete {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
            } else {
                Image(systemName: "circle")
                    .foregroundColor(.gray)
            }
        }
    }
    
    private var iconName: String {
        switch stepType {
        case .information:
            return "person.circle"
        case .preferences:
            return "slider.horizontal.3"
        case .emotion:
            return "heart.circle"
        case .custom:
            return "pencil.circle"
        case .import:
            return "square.and.arrow.down.on.square"
        }
    }
}
