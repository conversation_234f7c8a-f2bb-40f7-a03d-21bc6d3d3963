//
//  UpdatePromptView.swift
//  ChatAdvisor
//
//  版本更新提示视图 - 简约美观版本
//  采用现代化设计，支持强制更新和可选更新模式
//

import SwiftUI
import Localize_Swift

/// 版本更新提示视图
struct UpdatePromptView: View {
    
    // MARK: - 属性
    let versionControl: VersionControl
    let onUpdate: () -> Void
    let onLater: (() -> Void)?
    let onSkip: (() -> Void)?
    
    // MARK: - 状态
    @State private var isPresented = false
    @State private var pulseAnimation = false
    @Environment(\.colorScheme) var colorScheme
    // {{ AURA-X: Add - Release 环境下为可选更新提供“不再提示本版本”选项. Confirmed via mcp-feedback-enhanced }}
    @State private var doNotRemindThisVersion: Bool = false
    
    // MARK: - 计算属性
    private var isForceUpdate: Bool {
        versionControl.updateType.isForceUpdate
    }
    
    private var primaryColor: Color {
        isForceUpdate ? .red : .accentColor
    }
    
    private var titleText: String {
        isForceUpdate 
            ? "version_update_force_title".localized()
            : "version_update_optional_title".localized()
    }
    
    // MARK: - 主视图
    var body: some View {
        ZStack {
            // 背景遮罩
            backgroundOverlay
            
            // 主要内容
            mainCard
        }
        .onAppear {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                isPresented = true
            }
            
            withAnimation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true)) {
                pulseAnimation = true
            }
        }
    }
    
    // MARK: - 背景遮罩
    private var backgroundOverlay: some View {
        Rectangle()
            .fill(.ultraThinMaterial)
            .ignoresSafeArea()
            .opacity(isPresented ? 1 : 0)
            .onTapGesture {
                if !isForceUpdate {
                    #if DEBUG
                    dismissView { onLater?() }
                    #else
                    if doNotRemindThisVersion {
                        dismissView { onSkip?() }
                    } else {
                        dismissView { onLater?() }
                    }
                    #endif
                }
            }
    }
    
    // MARK: - 主卡片
    private var mainCard: some View {
        VStack(spacing: 24) {
            // 内容区域
            contentSection
            
            // 按钮区域
            buttonsSection
        }
        .padding(32)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.1), radius: 20, x: 0, y: 10)
        )
        .padding(.horizontal, 24)
        .scaleEffect(isPresented ? 1 : 0.7)
        .opacity(isPresented ? 1 : 0)
    }

    
    // MARK: - 状态标签
    private var statusBadge: some View {
        HStack(spacing: 6) {
            Image(systemName: isForceUpdate ? "exclamationmark.triangle.fill" : "arrow.up.circle.fill")
                .font(.caption)
                .foregroundColor(isForceUpdate ? .orange : .green)
            
            Text(isForceUpdate ? "version_update_required".localized() : "version_update_available".localized())
                .font(.caption)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            Capsule()
                .fill(.regularMaterial)
        )
    }
    
    // MARK: - 内容区域
    private var contentSection: some View {
        VStack(spacing: 20) {
            // 标题
            Text(titleText)
                .font(.title2)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)
            
            // 版本信息
            versionComparisonView
            
            // 更新说明
            if !versionControl.updateMessage.isEmpty {
                Text(versionControl.updateMessage)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(4)
            }

            // {{ AURA-X: Add - 仅在 Release 且为可选更新时显示“不再提示本版本”开关 }}
            #if !DEBUG
            if !isForceUpdate {
                Toggle(isOn: $doNotRemindThisVersion) {
                    Text("version_update_do_not_remind".localized())
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .toggleStyle(SwitchToggleStyle(tint: .accentColor))
            }
            #endif
        }
    }
    
    // MARK: - 版本对比
    private var versionComparisonView: some View {
        HStack(spacing: 20) {
            // 当前版本
            versionItem(
                label: "version_current".localized(),
                version: getCurrentVersion(),
                isHighlight: false
            )
            
            // 箭头
            Image(systemName: "arrow.right")
                .font(.title3)
                .foregroundColor(primaryColor)
                .scaleEffect(pulseAnimation ? 1.2 : 1.0)
            
            // 新版本
            versionItem(
                label: "version_latest".localized(),
                version: versionControl.latestVersion,
                isHighlight: true
            )
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.quaternary.opacity(0.5))
        )
    }
    
    // MARK: - 版本项
    private func versionItem(label: String, version: String, isHighlight: Bool) -> some View {
        VStack(spacing: 4) {
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(version)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(isHighlight ? primaryColor : .primary)
        }
    }
    
    // MARK: - 按钮区域
    private var buttonsSection: some View {
        VStack(spacing: 12) {
            // 主要更新按钮
            updateButton
            
            // 次要按钮（仅可选更新时显示）
            if !isForceUpdate {
                secondaryButtons
            }
        }
    }
    
    // MARK: - 更新按钮
    private var updateButton: some View {
        Button(action: handleUpdate) {
            HStack(spacing: 8) {
                Image(systemName: "arrow.down.app.fill")
                    .font(.headline)
                
                Text("version_update_button_update".localized())
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 52)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [primaryColor, primaryColor.opacity(0.8)],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
            )
        }
        .buttonStyle(ScaleButtonStyle())
    }
    
    // MARK: - 次要按钮
    private var secondaryButtons: some View {
        HStack(spacing: 12) {
            Button("version_update_button_later".localized()) {
                handleLater()
            }
            .buttonStyle(SecondaryButtonStyle())
            
            Button("version_update_button_skip".localized()) {
                handleSkip()
            }
            .buttonStyle(SecondaryButtonStyle())
        }
    }
    
    // MARK: - 方法
    private func handleUpdate() {
        generateHapticFeedback(.medium)
        onUpdate()
    }
    
    private func handleLater() {
        generateHapticFeedback(.light)
        #if DEBUG
        dismissView { onLater?() }
        #else
        if doNotRemindThisVersion {
            dismissView { onSkip?() }
        } else {
            dismissView { onLater?() }
        }
        #endif
    }
    
    private func handleSkip() {
        generateHapticFeedback(.light)
        dismissView {
            onSkip?()
        }
    }
    
    private func dismissView(completion: (() -> Void)? = nil) {
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            isPresented = false
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            completion?()
        }
    }
    
    private func generateHapticFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
    
    private func getCurrentVersion() -> String {
        Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "1.0.0"
    }
    
    private func getAppIcon() -> UIImage? {
        guard let iconsDictionary = Bundle.main.infoDictionary?["CFBundleIcons"] as? [String: Any],
              let primaryIconsDictionary = iconsDictionary["CFBundlePrimaryIcon"] as? [String: Any],
              let iconFiles = primaryIconsDictionary["CFBundleIconFiles"] as? [String],
              let lastIcon = iconFiles.last else {
            return nil
        }
        return UIImage(named: lastIcon)
    }
}

// MARK: - 按钮样式
struct ScaleButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.secondary)
            .frame(maxWidth: .infinity)
            .frame(height: 44)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(.quaternary.opacity(0.5))
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}