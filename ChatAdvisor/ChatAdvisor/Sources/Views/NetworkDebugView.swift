//
//  NetworkDebugView.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/1/29.
//

import SwiftUI

struct NetworkDebugView: View {
    @StateObject private var debugManager = NetworkDebugManager.shared
    @Environment(\.presentationMode) var presentationMode
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 当前端点信息
                    currentEndpointSection
                    
                    // 端点测试区域
                    endpointTestSection
                    
                    // 网络诊断信息
                    networkDiagnosticsSection
                    
                    // 应用调试信息
                    appDebugInfoSection
                }
                .padding()
            }
            .navigationTitle("网络调试")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("关闭") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: Button("刷新") {
                    Task {
                        await debugManager.pingAllEndpoints()
                    }
                }
            )
        }
        .alert(isPresented: $showingAlert) {
            Alert(title: Text("提示"), message: Text(alertMessage), dismissButton: .default(Text("确定")))
        }
        .onAppear {
            Task {
                await debugManager.pingAllEndpoints()
            }
        }
    }
    
    // MARK: - 当前端点信息
    private var currentEndpointSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("当前端点信息")
                .font(.headline)
                .foregroundColor(.primary)
            
            let currentInfo = debugManager.getCurrentEndpointInfo()
            
            InfoRow(title: "端点名称", value: currentInfo.name)
            InfoRow(title: "服务器地址", value: currentInfo.url)
            InfoRow(title: "描述", value: currentInfo.description)
            InfoRow(title: "网络状态", value: debugManager.networkStatus)
            
            HStack {
                Button("重置为自动选择") {
                    debugManager.resetToAutoEndpoint()
                    alertMessage = "已重置为自动端点选择"
                    showingAlert = true
                }
                .buttonStyle(.bordered)
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 端点测试区域
    private var endpointTestSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("端点测试")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                if debugManager.isDebugging {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            
            ForEach(debugManager.getAllEndpoints(), id: \.name) { endpoint in
                EndpointTestRow(
                    endpoint: endpoint,
                    result: debugManager.debugResults.first { $0.endpoint == endpoint.name },
                    onPing: {
                        Task {
                            let result = await debugManager.pingEndpoint(endpoint.name, url: endpoint.url)
                            if let index = debugManager.debugResults.firstIndex(where: { $0.endpoint == endpoint.name }) {
                                debugManager.debugResults[index] = result
                            } else {
                                debugManager.debugResults.append(result)
                            }
                        }
                    },
                    onSwitch: {
                        debugManager.switchToEndpoint(endpoint.name)
                        alertMessage = "已切换到 \(endpoint.description)"
                        showingAlert = true
                    }
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 网络诊断信息
    private var networkDiagnosticsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("网络诊断")
                .font(.headline)
                .foregroundColor(.primary)
            
            let diagnostics = debugManager.getNetworkDiagnostics()
            
            if let deviceInfo = diagnostics["deviceInfo"] as? [String: Any] {
                InfoRow(title: "设备型号", value: deviceInfo["model"] as? String ?? "未知")
                InfoRow(title: "系统版本", value: "\(deviceInfo["systemName"] as? String ?? "iOS") \(deviceInfo["systemVersion"] as? String ?? "未知")")
            }
            
            if let appInfo = diagnostics["appInfo"] as? [String: Any] {
                InfoRow(title: "应用版本", value: "\(appInfo["version"] as? String ?? "未知") (\(appInfo["build"] as? String ?? "未知"))")
            }
            
            // 显示最近的ping结果统计
            if !debugManager.debugResults.isEmpty {
                let successCount = debugManager.debugResults.filter { $0.isSuccess }.count
                let totalCount = debugManager.debugResults.count
                let avgResponseTime = debugManager.debugResults.filter { $0.isSuccess }.map { $0.responseTime }.reduce(0, +) / Double(max(successCount, 1))
                
                InfoRow(title: "连接成功率", value: "\(successCount)/\(totalCount)")
                InfoRow(title: "平均响应时间", value: String(format: "%.0f ms", avgResponseTime))
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    // MARK: - 应用调试信息
    private var appDebugInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("调试信息")
                .font(.headline)
                .foregroundColor(.primary)
            
            let diagnostics = debugManager.getNetworkDiagnostics()
            
            if let debugInfo = diagnostics["debugInfo"] as? [String: Any] {
                InfoRow(title: "调试模式", value: debugInfo["debugModeEnabled"] as? Bool == true ? "开启" : "关闭")
                InfoRow(title: "调试端点", value: debugInfo["debugSelectedEndpoint"] as? String ?? "无")
                InfoRow(title: "发布模式", value: debugInfo["isRelease"] as? Bool == true ? "是" : "否")
            }
            
            // 缓存信息
            Button("清除网络缓存") {
                URLCache.shared.removeAllCachedResponses()
                RegionDetector.shared.clearCache()
                alertMessage = "网络缓存已清除"
                showingAlert = true
            }
            .buttonStyle(.bordered)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - 辅助视图组件

struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .foregroundColor(.primary)
                .fontWeight(.medium)
        }
        .font(.system(size: 14))
    }
}

struct EndpointTestRow: View {
    let endpoint: (name: String, url: String, description: String)
    let result: NetworkDebugResult?
    let onPing: () -> Void
    let onSwitch: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(endpoint.description)
                        .font(.system(size: 16, weight: .medium))
                    Text(endpoint.url)
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                HStack(spacing: 8) {
                    Button("Ping") {
                        onPing()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                    
                    Button("切换") {
                        onSwitch()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.small)
                }
            }
            
            if let result = result {
                HStack {
                    Circle()
                        .fill(result.isSuccess ? Color.green : Color.red)
                        .frame(width: 8, height: 8)
                    
                    Text(result.isSuccess ? "连接成功" : "连接失败")
                        .font(.system(size: 12))
                        .foregroundColor(result.isSuccess ? .green : .red)
                    
                    if result.isSuccess {
                        Text("• \(String(format: "%.0f ms", result.responseTime))")
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                    }
                    
                    if let error = result.error {
                        Text("• \(error)")
                            .font(.system(size: 12))
                            .foregroundColor(.red)
                            .lineLimit(1)
                    }

                    Spacer()
                }

                // 显示详细的服务器信息
                if result.isSuccess, let serverInfo = result.serverInfo {
                    VStack(alignment: .leading, spacing: 4) {
                        if let data = serverInfo["data"] as? [String: Any] {
                            if let network = data["network"] as? [String: Any] {
                                Text("客户端IP: \(network["clientIP"] as? String ?? "未知")")
                                    .font(.system(size: 10))
                                    .foregroundColor(.secondary)

                                if let country = network["country"] as? String, !country.isEmpty && country != "unknown" {
                                    Text("地区: \(country)")
                                        .font(.system(size: 10))
                                        .foregroundColor(.secondary)
                                }
                            }

                            if let server = data["server"] as? [String: Any] {
                                if let region = server["region"] as? String, !region.isEmpty && region != "unknown" {
                                    Text("服务器地区: \(region)")
                                        .font(.system(size: 10))
                                        .foregroundColor(.secondary)
                                }

                                if let processingTime = data["processingTime"] as? Double {
                                    Text("服务器处理时间: \(String(format: "%.0f ms", processingTime))")
                                        .font(.system(size: 10))
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                    .padding(.top, 4)
                }
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}
