//
//  ServerSendEventManager.swift
//  JunShi
//
//  Created by md on 2024/4/29.
//

import Foundation

// import LDSwiftEventSource
import EventSource // Import the EventSource library
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "SSEManager")

protocol ServerSendEventProtocol {
    associatedtype EventType: Codable

    var onEventReceived: ((EventType) -> Void)? { get set }
    var onError: ((Error) -> Void)? { get set }
    var onEnd: (() -> Void)? { get set }

    func connectToSSE(url: URL, method: String, httpBody: Data?, headers: [String: String])
    func disconnect()
}

class URLSessionSSEManager<T: Codable>: NSObject, URLSessionDataDelegate, ServerSendEventProtocol {
    typealias EventType = T
    var buffer: String = ""
    // 用于累积当前事件块中的 data: 内容（可能包含多个JSON或被截断的JSON片段）
    private var eventDataBuffer: String = ""
    var urlSession: URLSession!
    var task: URLSessionDataTask?
    var onEventReceived: ((T) -> Void)?
    var onError: ((Error) -> Void)?
    var onEnd: (() -> Void)?
    private let processQueue = DispatchQueue(label: "com.sanva.SSEManager")

    override init() {
        super.init()
        let config = URLSessionConfiguration.default
        // Modify - 延长超时时间以支持长连接SSE
        config.timeoutIntervalForRequest = 3600
        config.timeoutIntervalForResource = 3600
        // 禁用自动压缩，减少代理/中间件干扰（若服务器端已有控制，不会有副作用）
        config.httpAdditionalHeaders = [
            "Accept": "text/event-stream",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Accept-Encoding": "identity"
        ]
        urlSession = URLSession(configuration: config, delegate: self, delegateQueue: nil)
    }

    func connectToSSE(url: URL, method: String = "POST", httpBody: Data?, headers: [String: String]) {
        // 连接开始时重置缓冲区（串行队列内执行，避免跨线程并发写导致索引失效）
        processQueue.sync {
            self.buffer = ""
            self.eventDataBuffer = ""
        }

        var request = URLRequest(url: url)
        request.httpMethod = method
        // 合并必要的SSE头（不覆盖调用方同名自定义头）
        var mergedHeaders = headers
        if mergedHeaders["Accept"] == nil { mergedHeaders["Accept"] = "text/event-stream" }
        if mergedHeaders["Cache-Control"] == nil { mergedHeaders["Cache-Control"] = "no-cache" }
        if mergedHeaders["Connection"] == nil { mergedHeaders["Connection"] = "keep-alive" }
        if mergedHeaders["Accept-Encoding"] == nil { mergedHeaders["Accept-Encoding"] = "identity" }
        request.allHTTPHeaderFields = mergedHeaders
        request.httpBody = httpBody
        task = urlSession.dataTask(with: request)
        task?.resume()
    }

    func disconnect() {
        task?.cancel()
        // {{ AURA-X: Fix - 断开连接时清理缓冲区：统一在串行队列内修改，避免并发写引发的 String Index 越界 }}
        processQueue.async { [weak self] in
            self?.buffer = ""
            self?.eventDataBuffer = ""
        }
    }

    func urlSession(_: URLSession, dataTask _: URLSessionDataTask, didReceive data: Data) {
        // 实时流式处理：按事件块（以空行分隔）解析，确保尽快回调到上层
        guard let chunk = String(data: data, encoding: .utf8), !chunk.isEmpty else {
            logger.warning("Warn: Empty chunk received")
            return
        }

        processQueue.async { [weak self] in
            guard let self = self else { return }
            self.buffer += chunk
            self.processIncomingLocked()
        }
    }

    /// 增量逐行处理：不等待事件结束，只要当前累积的 data 能组成完整JSON就立即解码回调
    private func processIncomingLocked() {
        // 规范化换行，逐行消费 buffer
        buffer = buffer.replacingOccurrences(of: "\r\n", with: "\n")

        while let newlineRange = buffer.range(of: "\n") {
            let line = String(buffer[..<newlineRange.lowerBound])
            buffer = String(buffer[newlineRange.upperBound...])

            let trimmed = line.trimmingCharacters(in: .whitespaces)
            // 空行：一个事件结束。尝试在缓冲中解出剩余完整JSON，然后清空事件缓冲
            if trimmed.isEmpty {
                if !eventDataBuffer.isEmpty {
                    extractDecodeFromEventBuffer()
                    eventDataBuffer = ""
                }
                continue
            }

            // 注释行，跳过
            if trimmed.hasPrefix(":") { continue }

            // 解析 data: 前缀
            if trimmed.hasPrefix("data:") {
                let value = trimmed.dropFirst("data:".count).trimmingCharacters(in: .whitespaces)
                // [DONE] 立即结束
                if value == "[DONE]" {
                    DispatchQueue.main.async { [weak self] in
                        self?.onEnd?()
                    }
                    // 清空事件缓冲，准备下一次事件
                    eventDataBuffer = ""
                    continue
                }

                // 将本行追加到事件缓冲（按规范多行 data 以 \n 连接）
                if !eventDataBuffer.isEmpty { eventDataBuffer.append("\n") }
                eventDataBuffer.append(String(value))

                // 尝试即时从事件缓冲中提取并解码任何完整JSON对象
                extractDecodeFromEventBuffer()
            }
        }
    }

    /// 从当前事件缓冲中尽可能提取并解码一个或多个完整JSON对象
    private func extractDecodeFromEventBuffer() {
        guard !eventDataBuffer.isEmpty else { return }
        // 快速路径：整体是一个完整JSON
        if isCompleteJSON(eventDataBuffer) {
            decodeAndEmit(jsonString: eventDataBuffer)
            eventDataBuffer = ""
            return
        }

        // 慢路径：在缓冲中扫描多个JSON对象，逐个消费
        var braceCount = 0
        var inString = false
        var escapeNext = false
        var startIndex: String.Index? = nil
        var i = eventDataBuffer.startIndex
        var lastConsumedEnd: String.Index? = nil

        while i < eventDataBuffer.endIndex {
            let char = eventDataBuffer[i]
            if escapeNext {
                escapeNext = false
            } else if char == "\\" {
                escapeNext = true
            } else if char == "\"" {
                inString.toggle()
            } else if !inString {
                if char == "{" {
                    if braceCount == 0 { startIndex = i }
                    braceCount += 1
                } else if char == "}" {
                    braceCount -= 1
                    if braceCount == 0, let start = startIndex {
                        let jsonString = String(eventDataBuffer[start...i])
                        decodeAndEmit(jsonString: jsonString)
                        lastConsumedEnd = i
                        startIndex = nil
                    }
                }
            }
            i = eventDataBuffer.index(after: i)
        }

        // 删除已消费的内容，保留未闭合的尾部供后续数据补全
        if let end = lastConsumedEnd {
            let next = eventDataBuffer.index(after: end)
            eventDataBuffer = String(eventDataBuffer[next...])
        }
    }

    // 旧的块级解析已被逐行增量解析取代

    private func decodeAndEmit(jsonString: String) {
        guard let jsonData = jsonString.data(using: .utf8) else { return }
        do {
            let decoded = try JSONDecoder().decode(T.self, from: jsonData)
            DispatchQueue.main.async { [weak self] in
                self?.onEventReceived?(decoded)
            }
        } catch {
            // 解码失败仅记录日志，不中断后续事件
            logger.error("Decode failed: \(String(describing: error)) json: \(jsonString.prefix(200))")
        }
    }

    /// 验证JSON字符串是否完整
    private func isCompleteJSON(_ jsonString: String) -> Bool {
        let trimmed = jsonString.trimmingCharacters(in: .whitespacesAndNewlines)

        // 基本检查：必须以 { 开始，以 } 结束
        guard trimmed.hasPrefix("{") && trimmed.hasSuffix("}") else {
            return false
        }

        // 计算大括号的平衡性
        var braceCount = 0
        var inString = false
        var escapeNext = false

        for char in trimmed {
            if escapeNext {
                escapeNext = false
                continue
            }

            if char == "\\" {
                escapeNext = true
                continue
            }

            if char == "\"" {
                inString.toggle()
                continue
            }

            if !inString {
                if char == "{" {
                    braceCount += 1
                } else if char == "}" {
                    braceCount -= 1
                }
            }
        }

        // JSON完整当且仅当大括号平衡（braceCount == 0）
        return braceCount == 0
    }

    func urlSession(_: URLSession, task _: URLSessionTask, didCompleteWithError error: (any Error)?) {
        // {{ AURA-X: Fix - 连接完成时清理缓冲区：统一在串行队列内修改，避免并发写引发的 String Index 越界 }}
        processQueue.async { [weak self] in
            self?.buffer = ""
            self?.eventDataBuffer = ""
        }

        // 如果是取消则不处理
        if let error {
            if (error as NSError).code == NSURLErrorCancelled {
                return
            }
            // {{ AURA-X: Modify - 修复主线程更新问题，确保错误回调在主线程执行 }}
            DispatchQueue.main.async { [weak self] in
                self?.onError?(error)
            }
            logger.error("Error occurred: \(error)")
        } else {
            // {{ AURA-X: Modify - 修复主线程更新问题，确保结束回调在主线程执行 }}
            DispatchQueue.main.async { [weak self] in
                self?.onEnd?()
            }
        }
    }
}



protocol SSEManagerProtocol {
    associatedtype EventType: Codable

    var onEventReceived: ((EventType) -> Void)? { get set }
    var onError: ((Error) -> Void)? { get set }
    var onEnd: (() -> Void)? { get set }

    func connectToSSE(url: URL, method: String, httpBody: Data?, headers: [String: String])
    func disconnect()
}
