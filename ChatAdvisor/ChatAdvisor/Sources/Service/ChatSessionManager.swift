//
//  ChatSessionManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/02.
//

import Foundation
import SwiftUI
import Combine
import OSLog

/// 会话状态枚举
enum ChatSessionState {
    case idle
    case loading
    case loaded
    case error(String)
    case switching
}

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "ChatSessionManager")

/// 会话管理器 - 统一管理所有会话相关的状态和操作
@MainActor
class ChatSessionManager: ObservableObject {
    static let shared = ChatSessionManager()
    
    // MARK: - Published Properties
    
    @Published private(set) var currentSession: ChatSession?
    @Published private(set) var sessionState: ChatSessionState = .idle
    @Published private(set) var cachedSessions: [String: ChatSession] = [:]
    @Published private(set) var isPreloading: Bool = false
    @Published private(set) var performanceMetrics: SessionPerformanceMetrics = SessionPerformanceMetrics()
    
    // {{ AURA-X: Modify - 添加预加载状态管理，避免界面闪烁. Approval: mcp-feedback-enhanced(ID:20250129009). }}
    @Published private(set) var hasHistoryData: Bool = false
    @Published private(set) var preloadedChatId: String? = nil
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
    private let maxCachedSessions = 10
    private let preloadThreshold = 3
    private var sessionAccessTimes: [String: Date] = [:]
    private var operationQueue = OperationQueue()
    
    // {{ AURA-X: Modify - 预加载状态标记. Approval: mcp-feedback-enhanced(ID:20250129009). }}
    private var isPreloadingComplete: Bool = false
    
    // MARK: - Initialization
    
    private init() {
        setupOperationQueue()
    }
    
    private func setupOperationQueue() {
        operationQueue.maxConcurrentOperationCount = 3
        operationQueue.qualityOfService = .userInitiated
    }
    

    
    // MARK: - Public Methods
    
    /// 切换到指定会话
    func switchToSession(chatId: String, chatListViewModel: ChatListViewModel) async throws {
        // 性能监控已移除

        sessionState = .switching

        do {
            // 1. 检查缓存
            if let cachedSession = cachedSessions[chatId] {
                await applyCachedSession(cachedSession)
                recordPerformance(operation: .sessionSwitch, duration: 0, fromCache: true)
                return
            }

            // 2. 尝试从数据库加载
            sessionState = .loading
            let chat: Chat

            do {
                chat = try await loadChatFromDatabase(chatId: chatId)
            } catch ChatSessionError.chatNotFound {
                // 如果数据库中找不到会话，可能是新创建的会话，尝试创建新会话
                logger.info("数据库中未找到会话 \(chatId)，尝试创建新会话")
                chat = try await createNewChatForSession(chatId: chatId)
            }

            // 若为新建或无用户消息的会话，作为草稿加入左侧列表，确保立即可见
            if chat.messages.isEmpty {
                await MainActor.run {
                    chatListViewModel.addDraftChat(chat)
                }
            }

            // 3. 创建新会话
            let session = try await createChatSession(from: chat, chatListViewModel: chatListViewModel)

            // 4. 应用会话
            await applySession(session)

            // 5. 缓存管理
            await manageCacheSize()

            // 6. 预加载相关会话
            Task {
                await preloadRelatedSessions(currentChatId: chatId)
            }

            recordPerformance(operation: .sessionSwitch, duration: 0, fromCache: false)
            sessionState = .loaded

        } catch {
            sessionState = .error(error.localizedDescription)
            recordPerformance(operation: .sessionSwitch, duration: 0, error: error)
            throw error
        }
    }
    
    /// 预加载会话
    func preloadSession(chatId: String) async {
        guard !isPreloading && cachedSessions[chatId] == nil else { return }
        
        isPreloading = true
        defer { isPreloading = false }
        
        do {
            let chat = try await loadChatFromDatabase(chatId: chatId)
            let session = ChatSession(
                id: chatId,
                chat: chat,
                viewModel: nil, // 预加载时不创建ViewModel
                lastAccessTime: Date(),
                isPreloaded: true
            )
            
            cachedSessions[chatId] = session
            logger.info("预加载会话成功: \(chatId)")
            
        } catch {
            logger.error("预加载会话失败: \(chatId), 错误: \(error)")
        }
    }
    
    /// 清理缓存
    func clearCache() {
        cachedSessions.removeAll()
        sessionAccessTimes.removeAll()
        currentSession = nil
        sessionState = .idle
    }
    
    /// 获取性能报告
    func getPerformanceReport() -> String {
        return performanceMetrics.generateReport()
    }
    
    // MARK: - Private Methods
    
    private func loadChatFromDatabase(chatId: String) async throws -> Chat {
        // 等待数据库准备
        let isDatabaseReady = await AdvisorDatabaseManager.shared.waitForDatabaseReady()
        guard isDatabaseReady else {
            throw ChatSessionError.databaseNotReady
        }

        // 加载会话数据
        guard let chat = await AdvisorDatabaseManager.shared.fetchChat(id: chatId) else {
            throw ChatSessionError.chatNotFound(chatId)
        }

        return chat
    }

    /// 为新会话创建Chat对象
    private func createNewChatForSession(chatId: String) async throws -> Chat {
        // 等待数据库准备
        let isDatabaseReady = await AdvisorDatabaseManager.shared.waitForDatabaseReady()
        guard isDatabaseReady else {
            throw ChatSessionError.databaseNotReady
        }

        // 创建新的Chat对象，使用指定的ID
        let newChat = Chat(id: chatId, messages: [])

        // 立即保存到数据库，确保后续操作能找到这个会话
        AdvisorDatabaseManager.shared.update(chat: newChat)

        logger.info("为会话切换创建新会话: chatId=\(chatId)")

        return newChat
    }
    
    private func createChatSession(from chat: Chat, chatListViewModel: ChatListViewModel) async throws -> ChatSession {
        // 创建ChatViewModel
        let viewModel = ChatViewModel(chatListViewModel: chatListViewModel)
        viewModel.currentModel = ChatViewModel.allModels.first ?? ChatsModel.default
        viewModel.setCurrentChat(chat: chat)
        
        // 创建会话对象
        let session = ChatSession(
            id: chat.id,
            chat: chat,
            viewModel: viewModel,
            lastAccessTime: Date(),
            isPreloaded: false
        )
        
        return session
    }
    
    private func applyCachedSession(_ session: ChatSession) async {
        // 如果是预加载的会话，需要创建ViewModel
        var updatedSession = session
        if session.isPreloaded {
            // 这里需要传入chatListViewModel，暂时使用nil
            let viewModel = ChatViewModel(chatListViewModel: nil)
            viewModel.currentModel = ChatViewModel.allModels.first ?? ChatsModel.default
            viewModel.setCurrentChat(chat: session.chat)
            
            updatedSession = ChatSession(
                id: session.id,
                chat: session.chat,
                viewModel: viewModel,
                lastAccessTime: Date(),
                isPreloaded: false
            )
        }
        
        currentSession = updatedSession
        sessionAccessTimes[session.id] = Date()
        cachedSessions[session.id] = updatedSession
    }
    
    private func applySession(_ session: ChatSession) async {
        currentSession = session
        sessionAccessTimes[session.id] = Date()
        cachedSessions[session.id] = session
    }
    
    private func manageCacheSize() async {
        guard cachedSessions.count > maxCachedSessions else { return }
        
        // 按最后访问时间排序，移除最旧的会话
        let sortedSessions = sessionAccessTimes.sorted { $0.value < $1.value }
        let sessionsToRemove = sortedSessions.prefix(cachedSessions.count - maxCachedSessions)
        
        for (sessionId, _) in sessionsToRemove {
            cachedSessions.removeValue(forKey: sessionId)
            sessionAccessTimes.removeValue(forKey: sessionId)
        }
        
        logger.info("缓存清理完成，当前缓存数量: \(self.cachedSessions.count)")
    }
    
    private func preloadRelatedSessions(currentChatId: String) async {
        // 获取最近的会话列表进行预加载
        let recentChats = await AdvisorDatabaseManager.shared.fetchChats(offset: 0, isArchived: false)
        let recentChatIds = recentChats.prefix(preloadThreshold).map { $0.id }
        
        for chatId in recentChatIds {
            if chatId != currentChatId && cachedSessions[chatId] == nil {
                await preloadSession(chatId: chatId)
            }
        }
    }
    
    private func recordPerformance(operation: PerformanceOperation, duration: TimeInterval, fromCache: Bool = false, error: Error? = nil) {
        performanceMetrics.recordOperation(operation, duration: duration, fromCache: fromCache, error: error)
    }
    
    private func analyzePerformance() {
        let report = performanceMetrics.generateReport()
        logger.info("性能分析报告:\n\(report)")
    }
}

// MARK: - Supporting Types

/// 会话对象
struct ChatSession {
    let id: String
    let chat: Chat
    let viewModel: ChatViewModel?
    let lastAccessTime: Date
    let isPreloaded: Bool
}

extension ChatSessionManager {
    // MARK: - Preload Management
    
    /// 设置预加载的聊天ID，避免界面闪烁
    /// - Parameter chatId: 预加载的聊天ID
    func setLastChatIdForPreload(_ chatId: String) {
        preloadedChatId = chatId
        hasHistoryData = true
        isPreloadingComplete = true
        logger.info("设置预加载聊天ID: \(chatId)")
    }
    
    /// 标记没有历史数据
    func setNoHistoryDataFlag() {
        hasHistoryData = false
        preloadedChatId = nil
        isPreloadingComplete = true
        logger.info("标记无历史数据状态")
    }
    
    /// 重置预加载状态
    func resetPreloadState() {
        hasHistoryData = false
        preloadedChatId = nil
        isPreloadingComplete = false
        logger.info("重置预加载状态")
    }
    
    /// 检查是否有预加载的数据
    var hasPreloadedData: Bool {
        return isPreloadingComplete && preloadedChatId != nil
    }
}

/// 会话管理错误
enum ChatSessionError: LocalizedError {
    case databaseNotReady
    case chatNotFound(String)
    case viewModelCreationFailed
    case cacheCorrupted
    
    var errorDescription: String? {
        switch self {
        case .databaseNotReady:
            return "数据库未准备好"
        case .chatNotFound(let chatId):
            return "会话未找到: \(chatId)"
        case .viewModelCreationFailed:
            return "视图模型创建失败"
        case .cacheCorrupted:
            return "缓存数据损坏"
        }
    }
}

/// 性能操作类型
enum PerformanceOperation {
    case sessionSwitch
    case messageLoad
    case cacheHit
    case preload
}

/// 会话性能指标
class SessionPerformanceMetrics: ObservableObject {
    @Published var sessionSwitchTimes: [TimeInterval] = []
    @Published var cacheHitRate: Double = 0.0
    @Published var errorCount: Int = 0
    @Published var totalOperations: Int = 0

    private var operationHistory: [(PerformanceOperation, TimeInterval, Bool, Error?)] = []

    func recordOperation(_ operation: PerformanceOperation, duration: TimeInterval, fromCache: Bool = false, error: Error? = nil) {
        operationHistory.append((operation, duration, fromCache, error))
        totalOperations += 1

        if let _ = error {
            errorCount += 1
        }

        switch operation {
        case .sessionSwitch:
            sessionSwitchTimes.append(duration)
        default:
            break
        }

        updateCacheHitRate()
    }
    
    private func updateCacheHitRate() {
        let cacheOperations = operationHistory.filter { $0.2 } // fromCache = true
        cacheHitRate = totalOperations > 0 ? Double(cacheOperations.count) / Double(totalOperations) : 0.0
    }
    
    func generateReport() -> String {
        let avgSwitchTime = sessionSwitchTimes.isEmpty ? 0 : sessionSwitchTimes.reduce(0, +) / Double(sessionSwitchTimes.count)
        let errorRate = totalOperations > 0 ? Double(errorCount) / Double(totalOperations) * 100 : 0.0
        
        return """
        性能指标报告:
        - 平均会话切换时间: \(String(format: "%.3f", avgSwitchTime))秒
        - 缓存命中率: \(String(format: "%.1f", cacheHitRate * 100))%
        - 错误率: \(String(format: "%.1f", errorRate))%
        - 总操作数: \(totalOperations)
        """
    }
}
