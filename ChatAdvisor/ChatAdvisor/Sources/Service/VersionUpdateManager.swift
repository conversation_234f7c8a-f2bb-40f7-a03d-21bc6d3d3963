//
//  VersionUpdateManager.swift
//  ChatAdvisor
//
//  版本更新管理器
//  负责处理版本检测、更新提示和应用商店跳转
//

import Foundation
import UIKit
import OSLog
import Moya

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "VersionUpdateManager")

/// 版本更新管理器
class VersionUpdateManager: ObservableObject {
    
    // MARK: - 单例
    static let shared = VersionUpdateManager()
    
    // MARK: - 发布属性
    @Published var showUpdateAlert = false
    @Published var updateInfo: VersionControl?
    @Published var isCheckingVersion = false
    
    // MARK: - 私有属性
    private var hasShownUpdateAlert = false
    private let userDefaults = UserDefaults.standard
    
    // MARK: - 用户偏好设置键
    private enum UserDefaultsKeys {
        static let lastVersionCheckDate = "lastVersionCheckDate"
        static let skippedVersion = "skippedVersion"
        static let lastShownUpdateVersion = "lastShownUpdateVersion"
    }
    
    private init() {}
    
    // MARK: - 公共方法
    
    /// 检查版本更新
    /// - Parameter force: 是否强制检查（忽略检查间隔）
    func checkForUpdates(force: Bool = false) {
        guard !isCheckingVersion else {
            logger.info("版本检查已在进行中，跳过重复检查")
            return
        }

        // 检查是否需要进行版本检测
        #if DEBUG
        // Debug 环境：每次都检查
        #else
        if !force && !shouldCheckForUpdates() {
            logger.info("跳过版本检查：未到检查时间或已检查过")
            return
        }
        #endif

        // 获取当前应用版本用于比较
        let currentVersion = Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "1.0.0"
        logger.info("开始检查版本更新... 当前版本: \(currentVersion)")

        isCheckingVersion = true

        // 使用现有的网络服务检查版本
        NetworkService.shared.requestMulti(VersionCheckTarget.checkVersion) { [weak self] (result: Result<NetworkResponse<VersionCheckResponse>, NetworkError>) in
            DispatchQueue.main.async {
                self?.isCheckingVersion = false
                self?.handleVersionCheckResponse(result)
            }
        }
    }
    
    /// 显示更新提示
    /// - Parameter versionControl: 版本控制信息
    func showUpdatePrompt(with versionControl: VersionControl) {
        // 确保在主线程执行UI更新
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            #if !DEBUG
            guard !self.hasShownUpdateAlert else {
                logger.info("更新提示已显示过，跳过重复显示")
                return
            }

            // 检查是否已跳过此版本（仅对可选更新有效）
            if !versionControl.updateType.isForceUpdate {
                let skippedVersion = self.userDefaults.string(forKey: UserDefaultsKeys.skippedVersion)
                if skippedVersion == versionControl.latestVersion {
                    logger.info("用户已跳过版本 \(versionControl.latestVersion)，不再显示提示")
                    return
                }
            }

            // 检查是否已为此版本显示过提示（强制更新除外）

            let lastShownVersion = self.userDefaults.string(forKey: UserDefaultsKeys.lastShownUpdateVersion)
            if lastShownVersion == versionControl.latestVersion && !versionControl.updateType.isForceUpdate {
                logger.info("已为版本 \(versionControl.latestVersion) 显示过提示，跳过")
                return
            }
            #endif

            self.updateInfo = versionControl
            self.showUpdateAlert = true
            self.hasShownUpdateAlert = true

            // 记录已显示的版本
            self.userDefaults.set(versionControl.latestVersion, forKey: UserDefaultsKeys.lastShownUpdateVersion)

            logger.info("显示版本更新提示：\(versionControl.latestVersion) (类型: \(versionControl.updateType.rawValue))")
        }
    }
    
    /// 用户选择立即更新
    func userDidChooseUpdate() {
        guard let updateInfo = updateInfo else {
            logger.error("更新信息为空，无法执行更新")
            return
        }
        if let downloadUrl = updateInfo.downloadUrl {
            openAppStore(with: downloadUrl)
        } else {
            openAppStore(with: "https://apps.apple.com/us/app/chat-advisor/id6526465428")
        }
        
        showUpdateAlert = false
        
        logger.info("用户选择立即更新，跳转到应用商店")
    }
    
    /// 用户选择稍后提醒
    func userDidChooseLater() {
        showUpdateAlert = false
        hasShownUpdateAlert = false
        
        logger.info("用户选择稍后提醒")
    }
    
    /// 用户选择跳过此版本
    func userDidSkipVersion() {
        guard let updateInfo = updateInfo else {
            logger.error("更新信息为空，无法跳过版本")
            return
        }

        // 记录跳过的版本
        userDefaults.set(updateInfo.latestVersion, forKey: UserDefaultsKeys.skippedVersion)
        showUpdateAlert = false

        logger.info("用户跳过版本：\(updateInfo.latestVersion)")
    }

    /// 重置版本检查状态（用于测试和调试）
    func resetVersionCheckState() {
        hasShownUpdateAlert = false
        showUpdateAlert = false
        updateInfo = nil
        userDefaults.removeObject(forKey: UserDefaultsKeys.lastVersionCheckDate)
        userDefaults.removeObject(forKey: UserDefaultsKeys.skippedVersion)
        userDefaults.removeObject(forKey: UserDefaultsKeys.lastShownUpdateVersion)
        logger.info("版本检查状态已重置")
    }
    
    /// 打开应用商店
    /// - Parameter urlString: 应用商店链接
    private func openAppStore(with urlString: String) {
        // 处理占位符链接
        let finalUrlString: String
        if urlString.contains("APP_STORE_URL_PLACEHOLDER") {
            // 使用应用的Bundle ID构建App Store链接
            if let bundleId = Bundle.main.bundleIdentifier {
                finalUrlString = "https://apps.apple.com/app/id\(bundleId)"
            } else {
                finalUrlString = "https://apps.apple.com"
            }
        } else {
            finalUrlString = urlString
        }
        
        guard let url = URL(string: finalUrlString) else {
            logger.error("无效的应用商店链接：\(finalUrlString)")
            return
        }
        
        if UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url) { success in
                if success {
                    logger.info("成功打开应用商店链接：\(finalUrlString)")
                } else {
                    logger.error("打开应用商店链接失败：\(finalUrlString)")
                }
            }
        } else {
            logger.error("无法打开应用商店链接：\(finalUrlString)")
        }
    }
    
    // MARK: - 私有方法
    
    /// 判断是否应该检查版本更新
    /// - Returns: 是否应该检查
    private func shouldCheckForUpdates() -> Bool {
        let lastCheckDate = userDefaults.object(forKey: UserDefaultsKeys.lastVersionCheckDate) as? Date
        let now = Date()
        
        // 如果从未检查过，或者距离上次检查超过24小时，则需要检查
        if let lastCheck = lastCheckDate {
            let timeInterval = now.timeIntervalSince(lastCheck)
            let shouldCheck = timeInterval > 24 * 60 * 60 // 24小时
            logger.info("距离上次检查：\(timeInterval/3600)小时，是否需要检查：\(shouldCheck)")
            return shouldCheck
        } else {
            logger.info("首次检查版本更新")
            return true
        }
    }
    
    /// 处理版本检查响应
    /// - Parameter result: 网络请求结果
    private func handleVersionCheckResponse(_ result: Result<NetworkResponse<VersionCheckResponse>, NetworkError>) {
        // 更新最后检查时间
        userDefaults.set(Date(), forKey: UserDefaultsKeys.lastVersionCheckDate)

        switch result {
        case .success(let response):
            if response.isSuccess, let versionData = response.data {
                logger.info("版本检查成功 - 需要更新: \(versionData.needUpdate), 更新类型: \(versionData.updateType)")

                // 确保downloadUrl不为空
                let downloadUrl = versionData.downloadUrl.isEmpty ?
                    "https://apps.apple.com/us/app/chat-advisor/id6526465428" :
                    versionData.downloadUrl

                let versionControl = VersionControl(
                    needUpdate: versionData.needUpdate,
                    updateType: VersionControl.UpdateType(rawValue: versionData.updateType) ?? .none,
                    latestVersion: versionData.latestVersion,
                    minimumVersion: versionData.minimumVersion,
                    updateMessage: versionData.updateMessage,
                    downloadUrl: downloadUrl,
                    versionCheckEnabled: true
                )

                if versionControl.needUpdate && versionControl.updateType != .none {
                    logger.info("检测到版本更新需求，准备显示更新提示")
                    showUpdatePrompt(with: versionControl)
                } else {
                    logger.info("当前版本已是最新版本或版本检测已禁用")
                }
            } else {
                logger.error("版本检查响应失败：\(response.message ?? "未知错误")")
            }

        case .failure(let error):
            logger.error("版本检查网络请求失败：\(error.localizedDescription)")
        }
    }
}

// MARK: - 版本检查响应数据结构

/// 版本检查响应数据
struct VersionCheckResponse: Codable {
    let needUpdate: Bool
    let updateType: String
    let currentVersion: String
    let latestVersion: String
    let minimumVersion: String
    let updateMessage: String
    let downloadUrl: String
    let platform: String
    let versionInfo: VersionInfo?

    /// 版本信息详情
    struct VersionInfo: Codable {
        let isLatest: Bool
        let isBelowMinimum: Bool
        let hasNewVersion: Bool
    }
}

// MARK: - 版本检查网络目标

enum VersionCheckTarget {
    case checkVersion
}

extension VersionCheckTarget: TargetType {
    var baseURL: URL {
        NetworkURL.current
    }
    
    var path: String {
        switch self {
        case .checkVersion:
            "/checkVersion"
        }
    }
    
    var method: Moya.Method {
        switch self {
        case .checkVersion:
            .get
        }
    }
    
    var task: Task {
        switch self {
        case .checkVersion:
            .requestPlain
        }
    }
    
    var headers: [String: String]? {
        var headers = APIBase.commonHeaders()
        headers["platform"] = "ios"
        return headers
    }
}
